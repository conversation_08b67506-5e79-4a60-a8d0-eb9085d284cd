<template>
  <div class="title">{{ $t(title) }}</div>
  <el-scrollbar>
    <div class="attrPanel xDevice">
      <el-form ref="formRef" class="attr-panel-form" :model="formData">
        <el-form-item :label="$t(title) + $t('lang.rms.fed.number')">
          <span>{{ formData.deviceCode }}</span>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.editDevice.pointType')">
          <span>{{ formData?.devicePointType }}</span>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.coverPoint')">
          <span>{{ formData?.cellCode }}</span>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.editDevice.plcCode')">
          <span>{{ formData?.plcCode }}</span>
        </el-form-item>

        <div class="demo-collapse">
          <el-collapse v-model="activeNames">
            <el-collapse-item :title="$t('lang.rms.fed.editDevice.attachmentDevice')" name="1">
              <el-form-item
                v-for="(item, key) in formData.deviceDtoList"
                :label="curLabel(item.deviceType)"
                class="part-label-inline"
              >
                <el-input disabled v-number-only v-model="item.plcCode" maxlength="11" class="part-label-inline-item-input" />
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item :title="$t('lang.rms.fed.editDevice.attachSinglePointWorkstation')" name="2">
              <template v-for="(item, key) in formData.stationDtoList">
                <el-form-item :label="$t('lang.rms.web.monitor.robot.workStationId')">
                  <el-input disabled v-number-only v-model="item.stationId" maxlength="11" />
                </el-form-item>
                <el-form-item :label="$t('lang.rms.fed.editDevice.workstationOrientation')">
                  <el-select disabled class="w180" v-model="item.direction">
                    <el-option key="east" :label="$t('lang.rms.fed.east')" value="east" />
                    <el-option key="south" :label="$t('lang.rms.fed.south')" value="south" />
                    <el-option key="west" :label="$t('lang.rms.fed.west')" value="west" />
                    <el-option key="north" :label="$t('lang.rms.fed.north')" value="north" />
                  </el-select>
                </el-form-item>
              </template>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-form>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, ComputedRef, ref, watch, reactive } from "vue";
import { useI18n } from "@packages/hook/useI18n";
import { useAttrStore } from "@packages/store/attr";
import { updateXDevice } from "@packages/api/map";
import { useDmsDeviceStore, DmsDevice } from "@packages/store/xDevice";

const { t } = useI18n();
const attrStore = useAttrStore();
const attrStoreRef = storeToRefs(useAttrStore());

let activeNames = ref(["1", "2"]);

let enableAddBCR = ref(true);
let enableAddSIZEVALIDATORSENSOR = ref(true);
let enableAddWEIGHINGSENSOR = ref(true);

const props = defineProps<{
  curData: {
    deviceCode: any;
    devicePointType: string;
    cellCode: string;
    plcCode: string;
    deviceDtoList: any[];
    stationDtoList: any[];
    deleteStationDtoList: any[];
    deleteDeviceDtoList: any[];
  };
  title: string;
  viewDevice: boolean
}>();

const dmsDeviceStore = useDmsDeviceStore();
let dmsDeviceDtoList = ref(dmsDeviceStore.getDmsDeviceDtoList());
// let deviceDtoList: any[] = reactive([]);
// let stationDtoList: any[] = reactive([]);
// let deleteStationDtoList: any[] = reactive([]);
// let deleteDeviceDtoList: any[] = reactive([]);

const formData = computed(() => {
  //Form 表单的数据 这里是用到了 device的 数据
  let deviceDtoList = props.curData.deviceDtoList;
  if (deviceDtoList.length > 0) {
    deviceDtoList.map(item => {
      if (item.deviceType === "BCR") enableAddBCR.value = false;
      if (item.deviceType === "SIZE_VALIDATOR_SENSOR") enableAddSIZEVALIDATORSENSOR.value = false;
      if (item.deviceType === "WEIGHING_SENSOR") enableAddWEIGHINGSENSOR.value = false;
    });
  } else {
    enableAddBCR.value = true;
    enableAddSIZEVALIDATORSENSOR.value = true;
    enableAddWEIGHINGSENSOR.value = true;
  }

  return props.curData;
});

const curSelectNode = ref(attrStoreRef.curNodeDataByIndex.value);


let result: any = reactive([]);
const formRef = ref();
/**
 * 可用的事件
 */
const emits = defineEmits<{
  (event: "trigger", data: any): void;
}>();

defineExpose({
  // getRef,//外部调用的 一些方法
});

const curLabel = (deviceType: any) => {
  console.log("deviceType", deviceType);
  switch (deviceType) {
    case "BCR":
      return t("lang.rms.fed.editDevice.BCRScanner");
    case "WEIGHING_SENSOR":
      return t("lang.rms.fed.editDevice.weighingInspection");
    case "SIZE_VALIDATOR_SENSOR":
      return t("lang.rms.fed.editDevice.appearanceInspection");
  }
};
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;

  font-size: 0px;

  &.xDevice {
    padding-bottom: 30px;
  }
  .attr-panel-form {
    padding: 10px;

    .part-label-inline-item-input {
      display: inline-flex !important;
      vertical-align: middle;
      margin-right: 32px;
      flex: 1;
    }
    .part-label-inline-item-button {
      display: inline-flex !important;
      vertical-align: middle;
      margin-right: 32px;
    }
  }
  .title {
    font-size: 20px;
    padding: 10px 10px 15px 10px;
  }

  .attr-button-area {
    padding: 15px 0 0 0;
  }
  .fRight {
    float: right;
  }
}
</style>
<style lang="scss">
.my-imput--number {
  border: 1px solid #dcdfe6;
  width: 100%;
  display: flex;
  border-radius: 4px;
  .el-input-number-mini {
    flex: 1;
  }
  :v-deep(.el-input__inner) {
    border: none !important;
  }
  .define-append {
    width: 40px;
    display: inline-block;
    background: #f5f7fa;
    padding: 0px 3px;
    border-left: none;
    height: 32px;
    line-height: 32px;
    color: #909399;
    font-size: 12px;
    text-align: center;
  }
}
</style>
