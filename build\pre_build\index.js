/* ! <AUTHOR> at 2024/11/07 */
import fs from "fs";
import { simpleGit, CleanOptions } from "simple-git";
import dayjs from "dayjs";

async function getVersion(mode) {
  const git = simpleGit();
  const { current } = await git.status(); // 当前分支
  let { latest: version_info } = await git.log({
    n: 1,
    format: { commitId: "%H", author: "%an", date: "%ad", message: "%s" },
  });
  version_info.date = dayjs(version_info.date).format("YYYY-MM-DD HH:mm"); // 日期格式化

  let filePath = "",
    consoleLog = "",
    fileContent = "";
  switch (mode) {
    case "development":
      consoleLog = "  ➜  开发环境：\n";
      filePath = "./_env_rms/version.dev.json";
      fileContent = JSON.stringify(
        { tag: current, commitId: "--", author: "yourself", date: "--", message: "--" },
        null,
        2,
      ); // 使用缩进格式化 JSON 字符串
      break;
    case "production":
      git.clean(CleanOptions.FORCE);
      consoleLog = "  ➜  生产环境：\n";
      const tag = current.slice(1).replace(/\b-project\b/g, ""); // 去掉分支前缀
      version_info.tag = `athena-fe-${tag}`; // 附加当前分支
      filePath = "./static/configs/version.config.json";
      fileContent = JSON.stringify(version_info, null, 2); // 使用缩进格式化 JSON 字符串
      break;
    case "preview":
      consoleLog = "  ➜  预览环境：\n";
      break;
  }

  console.log(consoleLog, version_info);

  if (filePath && fileContent) {
    fs.writeFileSync(filePath, fileContent);
  }
}

export { getVersion };
