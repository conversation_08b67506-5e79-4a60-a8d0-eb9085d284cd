<template>
  <geek-main-structure class="no-top-space">
    <rms-simple-form
      ref="searchForm"
      bg
      :loading="isLoading"
      :configs="formConfigs"
      :operations="formOperations"
      @handler="handlerForm"
    >
      <template #modelNameLabel="{ label }">
        {{ $t(label) }}{{ $t("lang.rms.fed.shelfCategor.name.msg") }}
      </template>
    </rms-simple-form>

    <rms-button-group
      :loading="isLoading"
      :btns="[
        {
          iconType: 'add',
          handler: 'row-add',
          label: 'lang.rms.fed.add',
          permissionCode: 'auth.rms.page.menu.containerModelManagement.Add',
        },
      ]"
      @handler="handlerOperation"
    />

    <section v-loading="isLoading" class="model-list">
      <el-empty v-if="!tableData?.length" class="empty" />
      <div v-for="(item, index) in tableData" :key="`${uniqueId}_${index}`" class="model-item">
        <model-box
          ref="modelBox"
          :id="`${uniqueId}_${index}_view`"
          :data="item"
          :isView="true"
          @handleClick="handlerOperation('row-edit', item)"
        />

        <div class="model-info">
          <span>{{ modelName(item) }}</span>

          <span>
            <i v-if="!item.used" class="icon-status" />
            <i v-else :title="$t('lang.rms.fed.modelUsedMsg')" class="icon-status used" />
            <el-icon
              v-if="!item.used && hasDeletePermission"
              @click="handlerOperation('row-del', item)"
              class="icon-delete"
            >
              <el-icon-delete />
            </el-icon>
          </span>
        </div>
      </div>
    </section>

    <rms-pagination :page="tablePage" @pageChange="pageChange" />

    <edit-dialog ref="editDialog" :categoryDict="categoryDict" @updateTableList="refreshTable" />
  </geek-main-structure>
</template>

<script>
import { mapState } from "pinia";
import { useRootMenuStore } from "@stores/rootMenuStore";
import { useBootConfigStore } from "@stores/bootConfigStore";
import RmsSimpleForm from "@plugins/pageComponents/rms-simple-form.vue";
import RmsButtonGroup from "@plugins/pageComponents/rms-button-group.vue";
import ModelBox from "./components/modelBox/index.vue";
import EditDialog from "./components/editDialog.vue";

export default {
  name: "ContainerModelManage",
  components: { RmsSimpleForm, RmsButtonGroup, ModelBox, EditDialog },
  data() {
    return {
      isLoading: false,
      formConfigs: [
        {
          prop: "modelName",
          tag: "input",
          defaultValue: "",
          label: "lang.rms.web.container.containerModel",
        },
        {
          prop: "modelCategoryList",
          tag: "select",
          defaultValue: [],
          label: "lang.rms.web.container.containerType",
          attrs: { "collapse-tags": true, multiple: true },
          options: [],
        },
      ],
      formOperations: [
        {
          label: "lang.rms.fed.query",
          type: "primary",
          handler: "query",
        },
        {
          label: "lang.rms.fed.reset",
          handler: "reset",
        },
      ],

      tableData: [],
      tablePage: { currentPage: 1, pageSize: 10, total: 0, disSizeLayout: true },

      categoryDict: [],
      uniqueId: Date.now(),
    };
  },
  computed: {
    ...mapState(useRootMenuStore, ["getBtnPermission"]),
    ...mapState(useBootConfigStore, ["conTypes"]),

    hasDeletePermission() {
      return this.getBtnPermission("auth.rms.page.menu.containerModelManagement.Delete");
    },
    modelName() {
      return item => {
        const dicts = this.categoryDict;
        const category = item?.extendJson?.subCategory || item?.modelCategory;

        let str = item.modelName || "";
        const obj = dicts.find(d => d.value === category);

        return `${str} ${obj?.label ? this.$t(obj.label) : category}`;
      };
    },
  },
  watch: {
    conTypes(data) {
      // 只有在配置模式下生效
      if (data.length && this.$route.path === "/bootConfiguration/containerModelManage") {
        this.$refs.searchForm.setData("modelCategoryList", data);
        this.$nextTick(() => {
          this.getTableList({ modelCategoryList: data });
        });
      }
    },
  },
  mounted() {
    this.getCategoryDict();
    this.getTableList();
  },
  methods: {
    handlerOperation(type, row) {
      switch (type) {
        case "row-add":
          this.$refs.editDialog.open(null);
          break;
        case "row-edit":
          this.$refs.editDialog.open(row);
          break;
        case "row-del":
          this.rowDel(row);
          break;
      }
    },
    handlerForm(type, formData) {
      switch (type) {
        case "query":
        case "reset":
          this.tablePage.currentPage = 1;
          this.getTableList(formData);
          break;
      }
    },

    rowDel(row) {
      this.$rmsConfirm({
        content: "lang.rms.fed.confirmDelete",
      }).then(() => {
        $req.post("/athena/containerModel/deleteContainerModel", { id: row.id }).then(res => {
          this.$success(this.$t("lang.rms.fed.deleteSuccessfully"));
          const { total, pageSize, currentPage } = this.tablePage;
          this.tablePage.currentPage = $utils.Tools.adjustPageOnDelete(
            total,
            pageSize,
            currentPage,
          );
          this.refreshTable();
        });
      });
    },

    pageChange(page) {
      Object.assign(this.tablePage, page);
      this.refreshTable();
    },
    refreshTable() {
      const formData = this.$refs.searchForm.getFormData();
      this.getTableList(formData);
    },
    getTableList(formData = {}) {
      this.isLoading = true;
      const { currentPage, pageSize } = this.tablePage;
      const url = `/athena/containerModel/findContainerModelByPage`;
      const params = Object.assign({}, formData, { currentPage, pageSize });
      $req
        .post(url, params)
        .then(res => {
          let data = res?.data || {};
          this.tableData = data.recordList || [];
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: data.currentPage || 1,
            total: data.recordCount || 0,
          });
          this.uniqueId = Date.now();
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    getCategoryDict() {
      $req.get("/athena/containerModel/containerModelCategory").then(res => {
        const data = res.data || [];
        const options = data.map(i => ({
          label: i.i18n,
          value: i.key,
          category: i.category,
        }));
        this.categoryDict = options;
        this.formConfigs[1].options = options;
      });
    },
  },
};
</script>
<style lang="less" scoped>
.model-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: 3px;
  .model-item {
    width: 20%;
    margin: 0 0 6px;
    padding: 5px;

    .model-info {
      .g-flex();
      width: 100%;
      padding: 3px;
      font-size: 13px;
      border: 1px solid #ebeef5;

      .icon-status {
        right: 20px;
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 12px;
        background-color: #909399;

        &.used {
          cursor: pointer;
          background-color: #67c23a;
        }
      }
      .icon-delete {
        margin-left: 5px;
        cursor: pointer;
        color: #f56c6c;
      }
    }
  }

  .empty {
    width: 100%;
    text-align: center;
  }
}

.operateBtns {
  padding-right: 10px;
  .addItem {
    width: 120px;
    height: 32px;
  }
}
.container-model-table-wrap {
  padding-top: 10px;
  height: calc(100% - 120px);
}

.success {
  color: #00b44b;
}
</style>
