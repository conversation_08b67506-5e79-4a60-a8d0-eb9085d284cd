<template>
  <div class="title">{{ $t(title) }}</div>
  <el-scrollbar>
    <div class="attrPanel xDevice">
      <el-form ref="formRef" class="attr-panel-form" :model="formData">
        <el-form-item :label="$t(title) + $t('lang.rms.fed.number')">
          <span>{{ formData.deviceCode }}</span>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.editDevice.pointType')">
          <span>{{ formData?.devicePointType }}</span>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.coverPoint')">
          <span>{{ formData?.cellCode }}</span>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.editDevice.plcCode')">
          <span>{{ formData?.plcCode }}</span>
        </el-form-item>

        <div class="demo-collapse">
          <el-collapse v-model="activeNames">
            <el-collapse-item :title="$t('lang.rms.fed.editDevice.attachmentDevice')" name="1">
              <el-form-item>
                <el-button type="primary" :disabled="!enableAddBCR" @click="addDeviceDto('BCR')">{{
                  $t("lang.rms.fed.editDevice.BCRScanner")
                }}</el-button>
                <el-button
                  type="primary"
                  :disabled="!enableAddSIZEVALIDATORSENSOR"
                  @click="addDeviceDto('SIZE_VALIDATOR_SENSOR')"
                  >{{ $t("lang.rms.fed.editDevice.appearanceInspection") }}</el-button
                >
                <el-button
                  type="primary"
                  :disabled="!enableAddWEIGHINGSENSOR"
                  @click="addDeviceDto('WEIGHING_SENSOR')"
                  >{{ $t("lang.rms.fed.editDevice.weighingInspection") }}</el-button
                >
              </el-form-item>

              <el-form-item
                v-for="(item, key) in formData.deviceDtoList"
                :label="curLabel(item.deviceType)"
                class="part-label-inline"
              >
                <el-input v-number-only v-model="item.plcCode" maxlength="11" class="part-label-inline-item-input" />
                <el-button type="danger" @click="delDeviceDto(item)" class="part-label-inline-item-button">
                  {{ $t("lang.rms.fed.delete") }}</el-button
                >
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item :title="$t('lang.rms.fed.editDevice.attachSinglePointWorkstation')" name="2">
              <el-form-item>
                <el-button type="primary" :disabled="formData.stationDtoList.length > 0" @click="addStationDto()"
                  >{{ $t("lang.rms.fed.addTo1")
                  }}{{ $t("lang.rms.fed.editDevice.attachSinglePointWorkstation") }}</el-button
                >
              </el-form-item>
              <template v-for="(item, key) in formData.stationDtoList">
                <el-form-item :label="$t('lang.rms.web.monitor.robot.workStationId')">
                  <el-input v-number-only v-model="item.stationId" maxlength="11" />
                </el-form-item>
                <el-form-item :label="$t('lang.rms.fed.editDevice.workstationOrientation')">
                  <el-select class="w180" v-model="item.direction">
                    <el-option key="east" :label="$t('lang.rms.fed.east')" value="east" />
                    <el-option key="south" :label="$t('lang.rms.fed.south')" value="south" />
                    <el-option key="west" :label="$t('lang.rms.fed.west')" value="west" />
                    <el-option key="north" :label="$t('lang.rms.fed.north')" value="north" />
                  </el-select>
                </el-form-item>
                <el-button type="danger" @click="delStationDto(item)"> {{ $t("lang.rms.fed.delete") }}</el-button>
              </template>
            </el-collapse-item>
          </el-collapse>
        </div>

        <el-form-item class="attr-button-area">
          <el-button class="fRight" type="primary" @click="onSubmit(formRef)">{{
            $t("lang.rms.fed.confirm")
          }}</el-button>
          <el-button class="fRight" @click="resetForm(formRef)">{{ $t("lang.rms.fed.cancel") }}</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, ComputedRef, ref, watch, reactive } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useI18n } from "@packages/hook/useI18n";
import { useAttrStore } from "@packages/store/attr";
import { updateXDevice } from "@packages/api/map";
import { useDmsDeviceStore, DmsDevice } from "@packages/store/xDevice";

const { t } = useI18n();
const attrStore = useAttrStore();
const attrStoreRef = storeToRefs(useAttrStore());

let activeNames = ref(["1", "2"]);

let enableAddBCR = ref(true);
let enableAddSIZEVALIDATORSENSOR = ref(true);
let enableAddWEIGHINGSENSOR = ref(true);

const props = defineProps<{
  curData: {
    deviceCode: any;
    devicePointType: string;
    cellCode: string;
    plcCode: string;
    deviceDtoList: any[];
    stationDtoList: any[];
    deleteStationDtoList: any[];
    deleteDeviceDtoList: any[];
  };
  title: string;
  viewDevice: boolean
}>();

const dmsDeviceStore = useDmsDeviceStore();
let dmsDeviceDtoList = ref(dmsDeviceStore.getDmsDeviceDtoList());
// let deviceDtoList: any[] = reactive([]);
// let stationDtoList: any[] = reactive([]);
// let deleteStationDtoList: any[] = reactive([]);
// let deleteDeviceDtoList: any[] = reactive([]);

const formData = computed(() => {
  //Form 表单的数据 这里是用到了 device的 数据
  let deviceDtoList = props.curData.deviceDtoList;
  if (deviceDtoList.length > 0) {
    deviceDtoList.map(item => {
      if (item.deviceType === "BCR") enableAddBCR.value = false;
      if (item.deviceType === "SIZE_VALIDATOR_SENSOR") enableAddSIZEVALIDATORSENSOR.value = false;
      if (item.deviceType === "WEIGHING_SENSOR") enableAddWEIGHINGSENSOR.value = false;
    });
  } else {
    enableAddBCR.value = true;
    enableAddSIZEVALIDATORSENSOR.value = true;
    enableAddWEIGHINGSENSOR.value = true;
  }

  return props.curData;
});

const curSelectNode = ref(attrStoreRef.curNodeDataByIndex.value);

const formValidateFn = () => {
  return true;
};

let result: any = reactive([]);
const getResult = (type: any, oldCode: any, newCode: any, parentDevice: any) => {
  console.log("getResult", type, oldCode, newCode);
  let oldDevice, newDevice;
  let tip = "";
  if (oldCode !== newCode) {
    //不同才会 考虑是否提交

    if (newCode) {
      //form 填入一个plcCode
      console.log("for 填新的 plcCode", dmsDeviceDtoList);
      newDevice = dmsDeviceDtoList.value.filter(item => item.deviceCode === newCode)[0];

      if (!newDevice) {
        //在dms 中不存在的plcCode
        const addOne: DmsDevice = {
          deviceCode: newCode,
          plcCode: newCode,
          deviceType: type,
          parentDeviceCode: parentDevice.deviceCode,
          mapId: parentDevice.mapId,
          cellCode: parentDevice.cellCode,
          floorId: parentDevice.floorId,
          devicePointType: type,
        };
        result.push(addOne);
        dmsDeviceDtoList.value.push(addOne);
      } else {
        if (newDevice.parentDeviceCode) {
          tip = type + " : " + newCode + " " + t("lang.rms.fed.occupied") + ";";
          return tip;
        } else {
          newDevice.parentDeviceCode = parentDevice.deviceCode;
        }

        result.push(newDevice);
      }
    }
    console.log("有旧的 没新的 需要删一删", oldCode, newCode);
    if (oldCode) {
      console.log("有旧的 没新的 需要删一删", oldCode, newCode);
      oldDevice = dmsDeviceDtoList.value.filter(item => item.deviceCode === oldCode)[0];
      // oldDevice.parentDeviceCode = null;//小纪 不让置空 让加needDelete 字段
      oldDevice.needDelete = true;
      result.push(oldDevice);

      const newDmsDataList = dmsDeviceDtoList.value.filter(item => item.deviceCode !== oldCode);
      console.log("有旧的 没新的 需要删一删==》删了？？newDmsDataList", newDmsDataList);
      dmsDeviceDtoList.value = newDmsDataList;
      console.log("有旧的 没新的 需要删一删==》删了？？dmsDeviceDtoList", dmsDeviceDtoList);
    }
  }
  return "";
};
const formRef = ref();

const onSubmit = async (formEl: FormInstance | undefined) => {
  console.log("submit!", formEl, formData);
  const newData = formData.value;

  let deviceDtoList = newData["deviceDtoList"];
  if (deviceDtoList && deviceDtoList.length > 0) {
    for (let i = 0; i < deviceDtoList.length; i++) {
      const element = deviceDtoList[i];
      element["deviceCode"] = element.plcCode;
    }
  }

  if (!formEl) return;
  // await formEl.validate((valid: any, fields: any) => {
  // if (valid) {
  console.log("submit!");
  // console.log("fields", fields);
  updateXDevice(newData).then(({ code, data, msg }) => {
    if (code === 0) {
      dmsDeviceStore.setDmsDeviceDtoList(dmsDeviceDtoList.value);
      resetForm(formEl);

      if (msg) {
        ElMessage({
          message: t(msg),
          type: "success",
        });
      }
    }
  });
  // } else {
  //   console.log("error submit!", fields);
  // }
  // });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  result = [];
  close();
};

const close = () => {
  emits("trigger", null);
};
/**
 * 可用的事件
 */
const emits = defineEmits<{
  (event: "trigger", data: any): void;
}>();

defineExpose({
  // getRef,//外部调用的 一些方法
});

const curLabel = (deviceType: any) => {
  console.log("deviceType", deviceType);
  switch (deviceType) {
    case "BCR":
      return t("lang.rms.fed.editDevice.BCRScanner");
    case "WEIGHING_SENSOR":
      return t("lang.rms.fed.editDevice.weighingInspection");
    case "SIZE_VALIDATOR_SENSOR":
      return t("lang.rms.fed.editDevice.appearanceInspection");
  }
};

const addDeviceDto = (type: any) => {
  const newData = formData.value;
  console.log("创建 子设备 ", type);
  switch (type) {
    case "BCR":
      newData.deviceDtoList.push({
        deviceCode: "",
        plcCode: "",
        cellCode: newData.cellCode,
        deviceType: "BCR",
        devicePointType: "BCR",
      });
      break;

    case "WEIGHING_SENSOR":
      newData.deviceDtoList.push({
        deviceCode: "",
        plcCode: "",
        cellCode: newData.cellCode,
        deviceType: "WEIGHING_SENSOR",
        devicePointType: "WEIGHING_SENSOR",
      });
      break;

    case "SIZE_VALIDATOR_SENSOR":
      newData.deviceDtoList.push({
        deviceCode: "",
        plcCode: "",
        cellCode: newData.cellCode,
        deviceType: "SIZE_VALIDATOR_SENSOR",
        devicePointType: "SIZE_VALIDATOR_SENSOR",
      });
      break;
  }
};

const delDeviceDto = (item: any) => {
  console.log("del--device", item);
  switch (item.deviceType) {
    case "BCR":
      enableAddBCR.value = true;
      break;
    case "SIZE_VALIDATOR_SENSOR":
      enableAddSIZEVALIDATORSENSOR.value = true;
      break;
    case "WEIGHING_SENSOR":
      enableAddWEIGHINGSENSOR.value = true;
      break;
  }
  const newData = formData.value;
  newData.deviceDtoList = newData.deviceDtoList.filter(p => p.deviceType !== item.deviceType);
  if (!item.id) {
    return;
  }

  newData.deleteDeviceDtoList.push(item);
};

const addStationDto = () => {
  const newData = formData.value;
  newData.stationDtoList.push({
    stationId: "",
    direction: "",
    cellCode: newData.cellCode,
  });
};

const delStationDto = (item: any) => {
  console.log("del--station", item);
  const newData = formData.value;
  newData.stationDtoList = [];
  if (!item.id) {
    return;
  }

  newData.deleteStationDtoList.push(item);
};
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;

  font-size: 0px;

  &.xDevice {
    padding-bottom: 30px;
  }
  .attr-panel-form {
    padding: 10px;

    .part-label-inline-item-input {
      display: inline-flex !important;
      vertical-align: middle;
      margin-right: 32px;
      flex: 1;
    }
    .part-label-inline-item-button {
      display: inline-flex !important;
      vertical-align: middle;
      margin-right: 32px;
    }
  }
  .title {
    font-size: 20px;
    padding: 10px 10px 15px 10px;
  }

  .attr-button-area {
    padding: 15px 0 0 0;
  }
  .fRight {
    float: right;
  }
}
</style>
<style lang="scss">
.my-imput--number {
  border: 1px solid #dcdfe6;
  width: 100%;
  display: flex;
  border-radius: 4px;
  .el-input-number-mini {
    flex: 1;
  }
  :deep(.el-input__inner) {
    border: none !important;
  }
  .define-append {
    width: 40px;
    display: inline-block;
    background: #f5f7fa;
    padding: 0px 3px;
    border-left: none;
    height: 32px;
    line-height: 32px;
    color: #909399;
    font-size: 12px;
    text-align: center;
  }
}
</style>
