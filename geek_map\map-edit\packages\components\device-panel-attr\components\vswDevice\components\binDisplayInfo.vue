<template>
  <div>
    <div class="vsw-input-container">
      <el-form-item
        :label="containerModelLabel"
        :prop="dataIndex + `.${side}.containerModelId`"
        :rules="[{ required: true, message: t('lang.rms.fed.pleaseEnter'), trigger: 'change' }]"
      >
        <el-select
          :disabled="viewDevice"
          v-model="formData[side].containerModelId"
          :placeholder="t('lang.rms.fed.choose')"
          style="width: 150px"
        >
          <el-option
            v-for="item in shelfModelList"
            :key="item.id"
            :value="item.id"
            :label="t(item.modelName)"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="side === 'left'"
        :label="t('lang.rms.map.deviceEdit.sortingWallCode')"
        :prop="dataIndex + '.left.vswCode'"
        :rules="[{ required: true, message: t('lang.rms.fed.pleaseEnter'), trigger: 'change' }]"
      >
        <el-input
          v-model="formData[side].vswCode"
          @input="limitInput"
          :disabled="viewDevice"
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item
        v-if="side === 'right'"
        :label="t('lang.rms.map.deviceEdit.sortingWallCode')"
        :prop="dataIndex + '.right.vswCode'"
        :rules="[{ required: true, message: t('lang.rms.fed.pleaseEnter'), trigger: 'change' }]"
      >
        <el-input
          v-model="formData[side].vswCode"
          @input="limitInput"
          :disabled="viewDevice"
          style="width: 150px"
        />
      </el-form-item>
    </div>

    <el-form-item class="ppp-relatvie-box">
      <div>{{ t("lang.rms.map.deviceEdit.binDisplay") }}</div>
      <el-popover
        placement="top-end"
        :width="200"
        trigger="click"
        :content="t('lang.rms.map.deviceEdit.binDisplayHintInfo')"
      >
        <template #reference>
          <span class="ppp-question"> </span>
        </template>
      </el-popover>
    </el-form-item>
    <!-- 格口布局 -->
    <el-form-item>
      <div class="table">
        <div class="row" v-for="(layer, layerIndex) in layers" :key="layerIndex">
          <div
            class="cell"
            v-for="(lattice, latticeIndex) in layer"
            :key="latticeIndex"
            :class="lattice.chuteType ? 'changeBGColorToYelloW' : 'changeBGColorToDefault'"
            @dblclick="toggleBinType(lattice)"
            :title="lattice.latticeCode"
          >
            <el-input
              :class="lattice.chuteType ? 'changeBGColorToYelloW' : 'changeBGColorToDefault'"
              v-model="lattice.latticeCode"
              :style="{ backgroundColor: lattice.color }"
              @input="limitLatticeInput(lattice)"
              :disabled="viewDevice"
            ></el-input>
          </div>
        </div>
      </div>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, ComputedRef, onMounted } from "vue";
import { useI18n } from "@packages/hook/useI18n";

const { t } = useI18n();

const props = defineProps<{
  side: string;
  formData: any;
  dataIndex: number;
  ContainerId: number;
  shelfModelList: any;
  viewDevice: boolean;
}>();

let containerId = ref();
let existingLattices = ref<any[]>();

onMounted(() => {
  const side = props.side === "left" ? "L" : "R";
  // 如果是第一次创建则默认填充shelfModelList下拉列表第一个值
  renderData.value.containerModelId = renderData.value?.containerModelId
    ? renderData.value?.containerModelId
    : props.shelfModelList.value[0]?.id;
  renderData.value.vswCode = renderData.value?.vswCode || `VSW${props.formData?.deviceCode}${side}`;
  containerId.value = props.ContainerId;
  existingLattices.value = renderData.value?.chuteRack?.lattices; // 接口保存的格口布局
});

const renderData = ref(props.formData[props.side]);
const shelfModelList = ref(props.shelfModelList);

const containerModelLabel = computed(() => {
  return props.side === "left"
    ? t("lang.rms.map.deviceEdit.sortingWallModelLeft")
    : t("lang.rms.map.deviceEdit.sortingWallModelRight");
});

// 获取容器模型行列数
const getContainerModelDetails = () => {
  const containerModelId = renderData.value?.containerModelId;
  const result: any = shelfModelList.value.find((item: any) => item.id === containerModelId);
  return result?.extendJson;
};

const lattices = ref<any[]>([]);
// 获取容器格口号 如果有则填充, 如果没有则创建
const getBinLayout = () => {
  const containerModelId = renderData.value?.containerModelId;
  if (
    containerModelId === containerId.value &&
    renderData.value?.chuteRack?.lattices?.length != 0
  ) {
    return existingLattices.value;
  } else {
    const extendJson = getContainerModelDetails();
    const chuteModelDetail = extendJson?.chuteModelDetail || [];
    const vswCode = renderData.value?.vswCode ?? "";
    lattices.value = []; // 先清空

    chuteModelDetail.forEach((detail: any) => {
      const layerLetter = String.fromCharCode("A".charCodeAt(0) + detail.layer - 1);
      for (let j = 0; j < detail.chuteNum; j++) {
        lattices.value.push({
          latticeCode: `${vswCode}${layerLetter}${j + 1}`,
          layer: detail.layer,
          line: j + 1,
          chuteType: 0,
          color: "#FFF",
        });
      }
    });

    return lattices.value;
  }
};

const layers = computed(() => {
  const extendJson = getContainerModelDetails();
  const chuteModelDetail = extendJson?.chuteModelDetail || [];
  let lattices = getBinLayout() || [];
  // 根据 chuteModelDetail 创建一个矩阵
  const matrix = chuteModelDetail.map((detail: any) => {
    return lattices.filter(lattice => lattice.layer === detail.layer);
  });

  return matrix.reverse() || []; // Reverse the matrix to match the UI
});

// 监听vswCode的变化
watch(
  () => renderData.value?.vswCode,
  (newVal, oldVal) => {
    // 修改分拣墙编号统一替换格口布局中的格口编码
    // 检查 lattices 是否存在
    let lattices = renderData.value.chuteRack?.lattices;
    if (!lattices || lattices?.length === 0) {
      // 如果不存在，根据 layers.value 创建一个新的 lattices 数组
      renderData.value.chuteRack = renderData.value.chuteRack || {};
      renderData.value.chuteRack.lattices = createLatticesArray(layers.value, newVal);
    } else {
      // 如果存在，更新现有的 lattices 数组
      renderData.value.chuteRack.lattices.forEach((lattice: any) => {
        if (lattice.latticeCode.startsWith(oldVal)) {
          lattice.latticeCode = lattice.latticeCode.replace(oldVal, newVal);
        }
      });
    }
  },
);

// 监听containerModelId的变化
watch(
  () => renderData.value?.containerModelId,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      const side = props.side === "left" ? "L" : "R";
      // 修改分拣墙编号统一替换格口布局中的格口编码
      renderData.value.vswCode =
        renderData.value?.vswCode || `VSW${props.formData?.deviceCode}${side}`;
      // 当containerModelId发生变化时，重新获取格口布局
      renderData.value.chuteRack = renderData.value.chuteRack || {};
      renderData.value.chuteRack.lattices = getBinLayout();
    }
  },
);

const createLatticesArray = (layers: any, vswCode: any) => {
  // 使用与之前相同的逻辑来创建 lattices 数组
  // 确保按照层和列的顺序添加元素
  const lattices: Array<any> = [];
  layers.forEach((layer: any, layerIndex: any) => {
    const layerLetter = String.fromCharCode("A".charCodeAt(0) + layerIndex);
    const side = props.side === "left" ? "L" : "R";
    layer.forEach((column: any, columnIndex: any) => {
      lattices.push({
        latticeCode: `${vswCode}${side}${layerLetter}${columnIndex + 1}`,
        layer: layerIndex + 1,
        line: columnIndex + 1,
        chuteType: 0,
        color: "#FFF",
      });
    });
  });
  return lattices;
};

const limitInput = (inputValue: any) => {
  const value = inputValue.replace(/[^a-z0-9]/gi, ""); // 只保留字母和数字
  renderData.value.vswCode = value;
};

const limitLatticeInput = (lattice: any) => {
  const value = lattice.latticeCode.replace(/[^a-z0-9]/gi, ""); // 只保留字母和数字
  lattice.latticeCode = value;
};

const toggleBinType = (lattice: any) => {
  // Update chuteType
  lattice.chuteType = lattice.chuteType ? 0 : 1;

  // 确保renderData.value.chuteRack.lattices对应latticeCode更新
  const index = renderData.value.chuteRack.lattices.findIndex(
    (item: any) => item.latticeCode === lattice.latticeCode,
  );
  if (index !== -1) {
    renderData.value.chuteRack.lattices[index] = lattice;
  }
};
</script>

<style lang="scss" scoped>
.vsw-lattice-code :deep(.el-input__wrapper) {
  box-shadow: none;
}
.changeBGColorToYelloW {
  background-color: #f1ca8a;
}
.changeBGColorToYelloW :deep(.el-input__wrapper) {
  background-color: #f1ca8a;
}
.changeBGColorToDefault {
  background-color: #fff;
}
.changeBGColorToDefault :deep(.el-input__wrapper) {
  background-color: #fff;
}

.table {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 2px;
}

.row {
  display: flex;
  width: 100%;
  gap: 2px;
}

.cell {
  flex: 1;
  border: 1px solid #d8dae2;
  text-align: center;
  height: 50px; /* Adjust height as needed */
  line-height: 50px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  box-sizing: border-box;
}

:deep(.cell .el-input__wrapper) {
  border: none; /* 去除边框 */
  box-shadow: none; /* 去除阴影 */
}
:deep(.cell .el-input__wrapper.is-focus) {
  box-shadow: none; /* 去除聚焦时的阴影 */
}
:deep(.cell .el-input__wrapper:hover) {
  box-shadow: none; /* 去除悬停时的阴影 */
}

.ppp-relatvie-box {
  position: relative;

  .ppp-question {
    display: block;
    width: 20px;
    height: 20px;
    background: url(data:image/png;base64,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)
      no-repeat;
    background-size: 100% 100%;

    color: #efefef;
    cursor: pointer;
  }
}

:deep(.el-form-item) {
  align-items: center;
}
.vsw-input-container :deep(.el-form-item__label) {
  width: 160px;
  display: flex;
  justify-content: flex-end;
}
</style>
