/**
 * 空负载模式 - 空载可用
 */
export const NOLOADAVAILABLE = 1;

/**
 * 空负载模式 - 负载可用
 */
export const LOADAVAILABLE = 2;

/**
 * 空负载模式 - 空负载可用
 */
export const EMPTYLOADAVAILABLE = 3;

// 单行道空负载模式
export const OBSTACLE_AVOIDANCE_LOADTYPE = [
  { label: "lang.rms.fed.noLoadAvailable", value: NOLOADAVAILABLE },
  { label: "lang.rms.fed.loadAvailable", value: LOADAVAILABLE },
  { label: "lang.rms.fed.emptyLoadAvailable", value: EMPTYLOADAVAILABLE },
];
