import { NodeAttrEditConf } from "@packages/type/editUiType";
import { DROP_CELL } from "@packages/configure/dict/nodeType";
import { FUNC_DIRECTION_DICT } from "@packages/configure/dict/shelfReturn";
import { GetFunctionalLabelsResult } from "@packages/api/map/type/getFunctionalLabels";
import { useI18n } from "@packages/hook/useI18n";
// 功能
export const FUNCTION_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  const { t } = useI18n();
  return {
    name: "func",
    tabTitle: "lang.rms.fed.func",
    labelWidth: "105px",
    formItem: [
      // 投递点功能
      {
        prop: "dropOffCell",
        label: t('lang.rms.fed.deliveryPointFunction'),
        component: "elCheckbox",
        style: {
          width: "140px",
        },
        border: true,
        onlyComponent: true,
        get(formData: { [k: string]: any }) {
          return !!formData.dropOffCell;
        },
        set(value: boolean, formData: { [k: string]: any }) {
          value ? formData.dropOffCell = {} : formData.dropOffCell = null;
        },
      },
      // 分拣投递方向
      {
        prop: "dropDirection",
        label: "lang.rms.fed.deliveryDirection",
        describe: 'lang.rms.fed.deliveryDirectionMsg0',
        labelWidth: "100px",
        component: "elSelect",
        data: FUNC_DIRECTION_DICT,
        multiple: true,
        get(formData: { [k: string]: any }) {
          return formData.dropOffCell?.dropDirection || [];
        },
        set(value: string[], formData: { [k: string]: any }) {
          formData.dropOffCell || (formData.dropOffCell = {});
          formData.dropOffCell.dropDirection = value;
        },
        appendAttrsFn(value: string, data: any) {
          return { condition: !!data.dropOffCell };
        },
      },
      // 分拣投递层数
      {
        prop: "dropLayers",
        label: "lang.rms.fed.numberDeliveryLayers",
        labelWidth: "100px",
        component: "elInput",
        describe: 'lang.rms.fed.numberDeliveryLayersMsg0',
        get(formData: { [k: string]: any }) {
          const dropLayers = formData.dropOffCell?.dropLayers || [];
          return dropLayers.join(',');
        },
        set(value: string, formData: { [k: string]: any }) {
          formData.dropOffCell || (formData.dropOffCell = {});
          const layers = value ? value.split(',') : [];
          formData.dropOffCell.dropLayers = layers;
        },
        appendAttrsFn(value: string, data: any) {
          return { condition: !!data.dropOffCell };
        },
      },
      // 分拣投递高度
      {
        prop: "dropHeights",
        label: "lang.rms.fed.deliveryHeight",
        labelWidth: "100px",
        component: "elInput",
        describe: 'lang.rms.fed.deliveryHeightMsg0',
        get(formData: { [k: string]: any }) {
          const dropHeights = formData.dropOffCell?.dropHeights || [];
          return dropHeights.join(',');
        },
        set(value: string, formData: { [k: string]: any }) {
          formData.dropOffCell || (formData.dropOffCell = {});
          const heights = value ? value.split(',') : [];
          formData.dropOffCell.dropHeights = heights;
        },
        appendAttrsFn(value: string, data: any) {
          return { condition: !!data.dropOffCell };
        },
      },
      // 可行驶类型
      // 5.6.1.0 中 可行驶类型改为了单选
      {
        prop: "sizeType",
        label: "lang.rms.fed.driveableType",
        describe: "lang.rms.api.result.warehouse.driveTypes",
        component: "elSelect",
        filterable: true,
        data: attrStore.findDistinctSizeTypeDict || [],
        get(fromData: { [k: string]: any }) {
          if (fromData.sizeType) {
            return fromData.sizeType;
          }
          return "";
        },
        set(value: string, fromData: { [k: string]: any }) {
          fromData.sizeType = value;
        },
      },
      // 新增功能
      {
        prop: "funcType",
        label: "lang.rms.fed.newFunc",
        component: "elSelect",
        multiple: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        defaultFirstOption: true,
        disabled: attrStore.isMultipleSelectMode || attrStore.isSingleSelectMode || attrStore.isFnChooseMode,
        set(value: string[], fromData: { [k: string]: any }) {
          fromData.funcType = value;
          fromData.functions || (fromData.functions = []);
          const { functions } = fromData;
          /**
           * 这里需要解析功能数据
           * 1. 先获遍历funcType, 为functions设置默认值, （已经有数据的不设置）
           * 2. 删除不存在funcType的functions数据
           */
          (value || []).forEach(funcType => {
            // 这里找到对应的functions内容
            const functionsItem = fromData.functions.find((item: any) => {
              return item.funcType === funcType;
            });

            // 如果找到则不用设置默认值了, 如果没有找到则需要设置一个默认值
            if (!functionsItem) {
              const defParams = attrStore.functionalLabels.find((item: any) => {
                return item.name === funcType;
              });

              // 解析默认值
              if (defParams) {
                const { params } = defParams;
                const def: { [k: string]: any } = {};
                Object.keys(params).forEach(paramsKey => {
                  const defVal = params[paramsKey]?.default || "";
                  if (typeof defVal === "object") {
                    def[paramsKey] = JSON.stringify(defVal);
                  } else {
                    def[paramsKey] = defVal;
                  }
                });

                // 添加默认值
                functions.push({ funcType, ...def });
              }
            }
          });

          // 删除不存在funcType的functions数据
          fromData.functions = functions.filter((item: any) => {
            return value.includes(item.funcType);
          });

          // ✨ 完结撒花
        },
        get(fromData: { [k: string]: any }) {
          if (!fromData) {
            return [];
          }
          return (fromData.functions || []).map((item: any) => {
            return item.funcType;
          });
        },
        data: attrStore.functionalLabels.map((item: GetFunctionalLabelsResult) => {
          // 这里如果是批量编辑需要禁用某些点
          const disabled = attrStore.selectNodes?.length > 1 && ['AVOID_CELL', 'BACKUP_CELL', 'QUEUE_CELL', 'RECYCLEID', 'WAIT_FOR_TASK_CELL', 'WAIT_CELL', 'VIA_CELL','DOCKING_CELL'].includes(item.name);
          return {
            label: item.text,
            value: item.name,
            disabled: disabled
          }
        }),
      },
      // 未保存提醒
      {
        onlyComponent: true,
        component: "elAlert",
        description: t("lang.rms.fed.unsavedReminderMsg1"), // lang.rms.fed.cellChangeLocationTip
        closable: false,
        type: "warning",
        showIcon: true,
        condition: !attrStore.curNodeCellCodeByIndex,
      },
      // 功能组件 function.conf
      {
        prop: "functions",
        appendSlotName: "functionsSlot",
        onlyComponent: true,
      },
    ],
  };
};
