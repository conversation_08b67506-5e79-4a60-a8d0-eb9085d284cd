import { useEditMap } from "@packages/hook/useEdit";
import _ from "lodash";
import { getDeviceTabConf } from "@packages/configure/rightFromData.conf";
import { NODE_AREA, NODE_CELL, NODE_LINE, NODE_DEVICE } from "@packages/configure/dict/nodeType";
const editMap = useEditMap();

/**
 * 根据禁用规则得到可用的配置项, TODO: 这里需要优化...
 * @param option
 * @param attrStore
 * @param element
 * @returns
 */
export function getTabConf(option: any, attrStore: any, element: any) {
  const { hiddenTabs, hiddenCodes, disabledCodes, type } = option;
  const getFn = {
    [NODE_DEVICE]: getDeviceTabConf,
  }[type as string];

  if (getFn) {
    return getFn(element)
      .filter(tabItem => {
        return !hiddenTabs.includes(tabItem.name);
      })
      .map(item => {
        const formItem = item.formItem
          .filter(item => !hiddenCodes.includes(item.prop || ""))
          .map(item => {
            if (item.prop && disabledCodes.includes(item.prop)) {
              return { ...item, disabled: true };
            }
            return item;
          });
        const data = { ...item, formItem };
        return data;
      });
  }
  return [];
}

/**
 * 根据name获取可用的单个tab内容
 * @param type
 * @param name
 */
export function getTabItemConfByName(type: string, name: string, element: any) {
  const getFn = {
    [NODE_DEVICE]: getDeviceTabConf,
  }[type as string];

  if (getFn) {
    return getFn(element).find(tabItem => tabItem.name === name) || [];
  }
  return [];
}

/**
 * 当资源有变动时更新资源
 * @param option
 * @param attrStore
 */
export function changeBaseTabData(option: any, attrStore: any) {
  // 这里需要针对独木桥做一些设置
  editMap.value?.updateElements({
    id: attrStore.layerName,
    data: [_.cloneDeep(option)],
  });
}

/**
 * 批量更新资源
 * @param option
 * @param attrStore
 */
export function batchChangeBaseTabData(option: any, attrStore: any) {
  editMap.value?.updateElements({
    id: attrStore.layerName,
    data: attrStore.selectNodes.map((item: any) => {
      return { ...item, ...option };
    }),
  });
}
