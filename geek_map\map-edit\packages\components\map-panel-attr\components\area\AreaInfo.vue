<template>
  <div class="area-info-con" ref="conRef">
    <div class="title" ref="titleRef">{{$t('lang.rms.fed.areaInfo')}}</div>
<!--    <div class="table-title">-->
<!--      <span>区域名称</span>-->
<!--      <span>区域类型</span>-->
<!--      <span>区域类型</span>-->
<!--    </div>-->
<!--    <RecycleScroller-->
<!--      class="area-list"-->
<!--      :items="areaData"-->
<!--      :item-size="32"-->
<!--      key-field="id"-->
<!--      v-slot="{ item }"-->
<!--    >-->
<!--      <div class="area-item">-->
<!--        <span class="area-name">{{item.areaName}}</span>-->
<!--        <span class="area-type">{{item.areaType}}</span>-->
<!--        <span class="area-select">选择</span>-->
<!--      </div>-->
<!--    </RecycleScroller>-->
<!--    <el-table-->
<!--      :data="areaData"-->
<!--      style="width: 100%"-->
<!--      max-height="100%"-->
<!--    >-->
    <el-table
      :data="areaData"
      style="width: 100%"
      :max-height="tableHRef"
    >
      <el-table-column
        show-overflow-tooltip
        prop="areaName"
        :label="$t('lang.rms.fed.mapArea.name')">
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="areaType"
        :label="$t('lang.rms.fed.mapArea.type')">
      </el-table-column>
      <el-table-column
        fixed="right"
        :label="$t('lang.rms.fed.operation')"
        width="60px"
      >
        <template #default="scope">
          <el-button link type="primary" size="small" @click="selectArea(scope.row)">{{$t('lang.rms.fed.enter')}}</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref,onMounted,onUnmounted } from "vue";
import { useEditMap } from "@packages/hook/useEdit";
const editMap = useEditMap()
const areaData = ref()
//获取dom
const conRef = ref()
const titleRef = ref()
const tableHRef = ref(0)
//获取区域数据
const getArea = () => {
  const data = editMap.value?.getLayerData('AREA') || []
  areaData.value = data
}
//选择区域
const selectArea = (row:any) => {
  const {id} = row
  editMap.value?.setSelected({layerName:'AREA',id})
  editMap.value?.search({layerName:'AREA',id})
}
//计算table最大高度
const comTableH = () => {
  const conH = conRef.value.offsetHeight
  const titleH = titleRef.value.offsetHeight
  const tableH = conH - titleH
  tableHRef.value = tableH
  console.log(tableH)
}
//初始化
onMounted(() => {
  comTableH()
  getArea()
})


</script>

<style scoped lang="scss">
.area-info-con{
  width: 320px;
  height: 100%;
  .title{
    padding-left: 8px;
    font-weight: 900;
    font-size: 16px;
    padding-top: 5px;
    padding-bottom: 5px;
    border-left: 3px solid #409eff;
  }
  //.area-list{
  //  color: #545454 !important;
  //  height: 800px;
  //  font-size: 16px;
  //  .area-item{
  //    line-height: 32px;
  //    .area-name{
  //
  //    }
  //    .area-type{
  //
  //    }
  //    .area-select{
  //      font-size: 14px;
  //      color:#409eff;
  //      cursor: pointer;
  //    }
  //  }
  //}
  p{
    line-height: 30px;
    color: red;
    text-align: center;
  }
  //background-color: #2c3e50;
}
</style>
