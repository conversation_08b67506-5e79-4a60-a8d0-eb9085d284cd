<template>
  <div class="vsw-device-container">
    <el-scrollbar>
      <div class="attrPanel vswDevicePanel">
        <el-form class="attr-panel-form" ref="formRef" :model="vswData">
          <el-collapse v-model="activeNames">
            <el-collapse-item
              v-for="(item, index) in vswData"
              :key="index"
              :title="showVswClassName(item)"
              :name="index"
            >
              <div class="vsw-input-container">
                <el-form-item :label="t('lang.rms.map.deviceEdit.dockingCode')">
                  <span>{{ item.dockingCode }}</span>
                </el-form-item>
                <el-form-item :label="t('lang.rms.map.deviceEdit.stationStopPointCode')">
                  <span>{{ item.stationId }}</span>
                </el-form-item>
                <div class="vsw-input-container">
                  <el-form-item
                    :label="t('lang.rms.map.deviceEdit.sortDeviceCode')"
                    :prop="index + '.plcCode'"
                    :rules="[
                      { required: true, message: t('lang.rms.fed.pleaseEnter'), trigger: 'change' },
                    ]"
                  >
                    <el-input
                      class="font-weight-700"
                      style="width: 150px"
                      v-model="item.plcCode"
                      :disabled="viewDevice"
                    />
                  </el-form-item>
                </div>
                <el-form-item :label="t('lang.rms.map.deviceEdit.haveConvey')">
                  <el-checkbox
                    v-model="item.haveConvey"
                    :disabled="viewDevice"
                    @change="handleHaveConveyChange(item)"
                  />
                </el-form-item>
                <el-form-item
                  v-if="item.haveConvey"
                  :label="t('lang.rms.map.deviceEdit.conveySide')"
                >
                  <el-select v-model="item.conveySide" style="width: 150px" :disabled="viewDevice">
                    <el-option :label="t('lang.rms.map.deviceEdit.sortDeviceLeft')" value="LEFT" />
                    <el-option
                      :label="t('lang.rms.map.deviceEdit.sortDeviceRight')"
                      value="RIGHT"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-if="!item.haveConvey"
                  :label="t('lang.rms.fed.vswWhetherParcelSize')"
                >
                  <el-checkbox v-model="item.whetherParcelSize" :disabled="viewDevice" />
                </el-form-item>
                <el-form-item
                  v-if="item.whetherParcelSize"
                  :label="t('lang.rms.fed.vswBigLocationX')"
                >
                  <el-input-number v-model="item.bigLocationX" :min="0" :disabled="viewDevice" />
                </el-form-item>
                <el-form-item
                  v-if="item.whetherParcelSize"
                  :label="t('lang.rms.fed.vswSmallLeftLocationX')"
                >
                  <el-input-number
                    v-model="item.smallLeftLocationX"
                    :min="0"
                    :disabled="viewDevice"
                  />
                </el-form-item>
                <el-form-item
                  v-if="item.whetherParcelSize"
                  :label="t('lang.rms.fed.vswSmallRightLocationX')"
                >
                  <el-input-number
                    v-model="item.smallRightLocationX"
                    :min="0"
                    :disabled="viewDevice"
                  />
                </el-form-item>
                <el-form-item :label="t('lang.rms.map.deviceEdit.startX')">
                  <el-input-number
                    v-model="item.dockingLocationX"
                    :min="0"
                    :disabled="viewDevice"
                  />
                </el-form-item>
                <el-form-item :label="t('lang.rms.map.deviceEdit.dockingHeight')">
                  <el-input-number v-model="item.dockingHeight" :min="0" :disabled="viewDevice" />
                </el-form-item>
                <el-form-item :label="t('lang.rms.map.deviceEdit.dockingLocationX')">
                  <el-input-number v-model="item.startX" :disabled="viewDevice" />
                </el-form-item>

                <el-form-item
                  :label="t('lang.rms.map.deviceEdit.sortingWall')"
                  class="sorting-wall"
                />
                <el-form-item :label="t('lang.rms.map.deviceEdit.sortingWallLocation')">
                  <el-checkbox
                    v-model="item.leftWall"
                    :label="t('lang.rms.fed.leftSide')"
                    size="large"
                    :disabled="viewDevice"
                  />
                  <el-checkbox
                    v-model="item.rightWall"
                    :label="t('lang.rms.fed.rightSide')"
                    size="large"
                    :disabled="viewDevice"
                  />
                </el-form-item>
              </div>
              <!-- 左侧 分拣墙组件 -->
              <binDisplayInfo
                v-if="item.leftWall && shelfModelList"
                :side="'left'"
                :form-data="item"
                :dataIndex="index"
                :shelfModelList="shelfModelList"
                :ContainerId.sync="item.left?.containerModelId"
                :viewDevice="viewDevice"
              />
              <!-- 右侧 分拣墙组件 -->
              <binDisplayInfo
                v-if="item.rightWall && shelfModelList"
                :side="'right'"
                :form-data="item"
                :dataIndex="index"
                :shelfModelList="shelfModelList"
                :ContainerId.sync="item.right?.containerModelId"
                :viewDevice="viewDevice"
              />
            </el-collapse-item>
          </el-collapse>

          <!-- 按钮区域 -->
          <el-form-item class="attr-button-area" v-if="!viewDevice">
            <el-button class="fRight" type="primary" @click="onSubmit(formRef)" :loading="loading">
              {{ $t("lang.rms.fed.confirm") }}
            </el-button>
            <el-button class="fRight" @click="resetForm(formRef)">
              {{ $t("lang.rms.fed.cancel") }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAttrStore } from "@packages/store/attr";
import { useAppStore } from "@packages/store/app";

import { computed, ref, watch, reactive } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";

import binDisplayInfo from "./binDisplayInfo.vue";
import { useI18n } from "@packages/hook/useI18n";

import { updateVswStation } from "@packages/api/map";

import { findVswStation, getShelfModelList } from "@packages/api/map";

const appStore = useAppStore();
const attrStore = storeToRefs(useAttrStore());

const props = defineProps<{
  vswData: any;
  viewDevice: boolean;
}>();

const { t } = useI18n();
const formRef = ref();

props.vswData.forEach((item: any) => {
  item.leftWall = item.left?.containerModelId ? true : false;
  item.rightWall = item.right?.containerModelId ? true : false;
});

const activeNames = ref(props.vswData.map((item: any, index: number) => index));

const showVswClassName = (data: any) => {
  return data.parkId
    ? `${t("lang.rms.fed.park")}: ${data.parkId}`
    : `${t("lang.configs.map.station.label")}: ${data.stationId}`;
};

const getNewVswDeviceData = async () => {
  const curSelectNode = attrStore.curNodeDataByIndex.value || {};
  const res = await findVswStation({
    mapId: appStore.mapId,
    stationId: (curSelectNode as any)["stationId"],
  });

  if (res?.code === 0) {
    props.vswData.value = ref(res.data); // 模拟数据
  }
};

const shelfModelList = ref();

const getShelfModelInfo = async () => {
  const res = await getShelfModelList({
    type: "vsw",
  });
  if (res.code === 0) {
    shelfModelList.value = ref(res.data);
  }
};

getShelfModelInfo();

const handleHaveConveyChange = (item: any) => {
  item.whetherParcelSize = !item.haveConvey;
};

const checkLatticesUnique = (data: any) => {
  for (let i = 0; i < data.length; i++) {
    let leftWallLattices = [];
    let rightWallLattices = [];
    if (data[i].left?.chuteRack?.lattices) {
      leftWallLattices = data[i].left.chuteRack.lattices.map((item: any) => item.latticeCode);
    }
    if (data[i].right?.chuteRack?.lattices) {
      rightWallLattices = data[i].right.chuteRack.lattices.map((item: any) => item.latticeCode);
    }
    const leftWallSet = new Set(leftWallLattices);
    const rightWallSet = new Set(rightWallLattices);
    if (
      leftWallSet.size !== leftWallLattices.length ||
      rightWallSet.size !== rightWallLattices.length
    ) {
      ElMessage({
        message: t("lang.rms.fed.latticeCodeIsNotUniquePleaseCheck"),
        type: "warning",
      });
      return false;
    }
  }
  return true;
};

const loading = ref(false);

const onSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      // 校验格口墙是否每个数据都是唯一的
      const result = checkLatticesUnique(props.vswData);
      if (!result) return;
      // 如果有一侧没有填写 根据后端约定 需要把这个“side”置空
      props.vswData.forEach((device: any) => {
        if ((device.left && device.left.containerModelId === null) || !device?.leftWall) {
          device.left = null;
        }
        if ((device.right && device.right.containerModelId === null) || !device?.rightWall) {
          device.right = null;
        }
      });
      loading.value = true;
      updateVswStation(props.vswData)
        .then(({ code, msg }) => {
          if (code === 0) {
            close();
            if (msg) {
              ElMessage({
                message: t(msg),
                type: "success",
              });
            }
          }
          // 更新数据
          getNewVswDeviceData();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      console.log("error submit!", fields);
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  getNewVswDeviceData();
  close();
};

const close = () => {
  emits("trigger", null);
};
/**
 * 可用的事件
 */
const emits = defineEmits<{
  (event: "trigger", data: any): void;
}>();
</script>

<style lang="scss" scoped>
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;
  padding: 10px;
  font-size: 0px;

  &.vswDevicePanel {
    padding-bottom: 30px;
  }

  .attr-panel-form {
    .sorting-wall ::v-deep .el-form-item__label {
      font-size: 17px;
      font-weight: bold;
    }
  }
}

:deep(.el-form-item) {
  align-items: center;
}

.vsw-input-container :deep(.el-form-item__label) {
  min-width: 160px;
  display: flex;
  justify-content: flex-end;
}
</style>
