<template>
  <span class="shrinkHandle" @click="trigger">
    <span v-if="isExtend" class="mapFont map-font-shouqikuaijin"></span>
    <span v-else class="mapFont map-font-zhankai"></span>
  </span>
  <div class="attrPanel" :class="{ isExtend, unExtend: !isExtend }">
    <!-- 全局点位编辑/优先级最高 -->
    <template v-if="isGlobalCell">
      <GlobalCell />
    </template>
    <!-- 复制 -->
    <template v-else-if="isCopyModel">
      <CopyPanel />
    </template>
    <!-- 批量编辑点位/优先级 2 -->
    <template v-else-if="isAddBatchCellPattern">
      <BatchCell />
    </template>
    <!-- 节点编辑 -->
    <template v-else-if="isCell && (isOneElement || isMultipleSelectMode || isBatchModifyCellFun) && !isOperationByBrush">
      <CellPanel v-if="curPanelConf" ref="nodeRef" :panelConf="curPanelConf" :title="cellTitle" />
    </template>
    <!-- 线段编辑 -->
    <template v-else-if="isLine && !isOperationByBrush">
      <LinePanel v-if="curPanelConf" ref="nodeRef" :panelConf="curPanelConf" :title="cellTitle" />
    </template>
    <!-- 设备编辑, 设备中的电梯会导致多选 -->
    <template v-else-if="isDevice && !isOperationByBrush">
      <DevicePanel v-if="curPanelConf" ref="nodeRef" :panelConf="curPanelConf" :title="cellTitle" />
    </template>
    <!-- 区域编辑 -->
    <template v-else-if="isArea && !isOperationByBrush">
      <AreaPanel v-if="curPanelConf" ref="nodeRef" :panelConf="curPanelConf" :title="cellTitle" />
    </template>
    <!-- 区域详情 -->
    <template v-else-if="curSelectType === 'AREA' && !isOperationByBrush">
      <AreaInfo></AreaInfo>
    </template>
    <!-- 如果没有 -->
    <template v-else>
      <InfoPanel />
    </template>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAttrStore } from "@packages/store/attr";
import { ref, watch, computed, ComputedRef } from "vue";
import { useEditMap } from "@packages/hook/useEdit";
import InfoPanel from "./components/info/index.vue";
import CellPanel from "./components/cell/index.vue";
import BatchCell from "./components/batchCell/index.vue";
import LinePanel from "./components/line/index.vue";
import AreaPanel from "./components/area/index.vue";
import AreaInfo from "./components/area/AreaInfo.vue";
import GlobalCell from "./components/globalCell/index.vue";
import DevicePanel from "./components/device/index.vue";
import CopyPanel from "./components/copyPanel/index.vue";
import { triggerEventListener } from "@packages/hook/useEvent";
import { getPanelConfByNodeType } from "@packages/hook/useRightFrom";
import { getComponentStore } from "@packages/store/piniaSync"
import {ADD_AREA} from "@packages/configure/toolPanel.conf"
import {
  NODE_CELL,
  NODE_LINE,
  NODE_DEVICE,
  NODE_AREA,
  NODE_TYPEKEY_MAP,
} from "@packages/configure/dict/nodeType";
const attrStore = storeToRefs(useAttrStore());
const menuStore = getComponentStore('menuStore')();
let menuStoreRef: any = null;
if (menuStore) {
  menuStoreRef = storeToRefs(menuStore);
}
const { isGlobalCell, isAddBatchCellPattern } = attrStore;
const curNodeType: ComputedRef<string | undefined> = computed(() => {
  return attrStore.curNodeTypeByIndex.value;
});

const areaSelectType = ref()
const areaSelectChange = (areaType:string) => {
  const f = allAreaType.value.filter(item => item.areaType === areaType)
  const ops = f[0]
  const editMap = useEditMap();
  // console.log(areaType)
  const attrStore = useAttrStore();
  attrStore.setDrawAreaType(areaType)
  triggerEventListener("map:addArea",{editMapRef:editMap.value,option:ops});
}
/**
 * 选择的类别
 */
const curSelectType: ComputedRef<string | undefined> = computed(() => {
  return attrStore.curSelectType.value;
});
/**
 * 是否仅选择了某一个元素
 */
const isOneElement: ComputedRef<boolean> = computed(() => {
  return attrStore.selectNodes.value.length === 1;
});

/**
 * 是否处于多选模式
 */
const isMultipleSelectMode: ComputedRef<boolean> = computed(() => {
  return attrStore.isMultipleSelectMode.value;
})

/**
 * 是否处于批量编辑功能点
 */
const isBatchModifyCellFun: ComputedRef<boolean> = computed(() => {
  if (menuStoreRef) {
    return menuStoreRef.isBatchModifyCellFun.value;
  }
  return false;
})

/**
 * 是否是格式刷模式
 */
const isOperationByBrush: ComputedRef<boolean> = computed(() => {
  return attrStore.curOperationBySpecial.value === 'formatBrush';
});

const drawAreaType: ComputedRef<string> = computed(() => {
  return attrStore.drawAreaType.value;
});

//是否是区域添加态
const isAreaAdd: ComputedRef<boolean> = computed(() => {
  return attrStore.isAreaAdd.value ;
});
watch(isAreaAdd,() => {
  areaSelectType.value = null
})
//所有的区域类型
const allAreaType: ComputedRef<any> = computed(() => {
  const areaOptions = ADD_AREA.areaOptions
  const selectOps = areaOptions.map(item => {
    const ops = item.option
    ops.active = true
    return ops
    // const {name,areaType,describe} = ops
    // return {
    //   name,
    //   areaType,
    //   describe
    // }
  })
  return selectOps
});
/**
 * 是否是一个单元格
 */
const isCell: ComputedRef<boolean> = computed(() => {
  return curNodeType.value === NODE_CELL;
});

/**
 * 是否是一个线段
 */
const isLine: ComputedRef<boolean> = computed(() => {
  return curNodeType.value === NODE_LINE;
});

/**
 * 是否是一个设备
 */
const isDevice: ComputedRef<boolean> = computed(() => {
  return curNodeType.value === NODE_DEVICE;
});

/**
 * 是否是一个区域
 * 正在绘制区域时也算
 */
const isArea: ComputedRef<boolean> = computed(() => {
  return curNodeType.value === NODE_AREA || drawAreaType.value !== "none";
});

const isCopyModel: ComputedRef<boolean> = computed(() => {
  if (!menuStoreRef) {
    return false;
  }
  return (<any>menuStoreRef).isCopyMode.value;
});

const curPanelConf = computed(() => {
  if (curNodeType.value) {
    const curSelectNode = attrStore.curNodeDataByIndex.value || {};
    // 这里使用any是因为ts无法识别curSelectNode中包含了NODE_TYPEKEY_MAP[curNodeType.value], 实际上始终是包含的
    const type = (curSelectNode as any)[NODE_TYPEKEY_MAP[curNodeType.value]];
    const panelConfByNodeType = getPanelConfByNodeType(curNodeType.value);
    return panelConfByNodeType[type] || panelConfByNodeType["def"];
  } else if (isArea.value && drawAreaType.value !== "none") {
    const panelConfByNodeType = getPanelConfByNodeType(NODE_AREA);
    return panelConfByNodeType[drawAreaType.value] || panelConfByNodeType["def"];
  }
});

const cellTitle: ComputedRef<string> = computed(() => {
  if (isCell.value) return "lang.rms.fed.cellEdit";
  if (isLine.value) return "lang.rms.fed.segmentEdit";
  if (isDevice.value) return "lang.rms.map.dmp.edit";
  if (isArea.value) return "lang.rms.fed.areaEditing";
  return "";
});

watch(curPanelConf, () => {
  setTimeout(() => {
    triggerEventListener("map:resize");
  }, 50);
});

const nodeRef = ref();
function getFromNode() {
  return nodeRef.value?.getRef();
}

// 展开与收起
const isExtend = ref(true);

function trigger() {
  if (isExtend.value) {
    isExtend.value = false;
  } else {
    isExtend.value = true;
  }
  setTimeout(() => {
    triggerEventListener("map:resize");
  }, 210);
}


// 当前选择的元素改变, 则自动展开
watch(attrStore.curNodeTypeByIndex, (node) => {

  if (attrStore.selectNodes.value.length === 1 && !attrStore.isMultipleSelectMode.value && !attrStore.isSingleSelectMode.value && !isExtend.value) {
    isExtend.value = true;
  }
}, {
  deep: true
});

defineExpose({
  getFromNode,
});
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 0px;
  position: relative;
  transition: all 0.2s;

  &.unExtend {
    max-width: 0px;
  }

  &.isExtend {
    max-width: 400px;
  }
}
.area-add-con{
  width: 320px;
  .title{
    padding-left: 8px;
    font-weight: 900;
    font-size: 16px;
    padding-top: 5px;
    padding-bottom: 5px;
    border-left: 3px solid #409eff;
  }
  .select-con{
    display: flex;
    align-items: center;
    padding: 10px 10px;
    .label{
      width: 80px;
      font-size: 14px;
    }
  }
}
.shrinkHandle {
  position: absolute;
  width: 20px;
  height: 30px;
  border: 1px solid #409eff;
  border-radius: 4px;
  background: #fff;
  top: 50%;
  left: -16px;
  transform: translate(0, -50%);
  border-right: none;
  z-index: 2;
  font-size: 0;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  color: #409eff;
}
</style>

<style lang="scss">
.attrPanel {
  .attr-tabs {
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__content {
      padding-top: 10px;
      padding-bottom: 30px;
    }
  }
}
</style>
