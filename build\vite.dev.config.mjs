/* ! <AUTHOR> at 2024/10/31 */
// 开发环境配置
import { defineConfig, loadEnv } from "vite";
import vueDevTools from "vite-plugin-vue-devtools";

export default defineConfig(mode => {
  const { VITE_RMS_API_URL, VITE_VUE_DEV_TOOLS, VITE_OPEN_HEADER_SECURITY } = loadEnv(
    mode,
    "./_env_rms",
  );

  let csp = {};
  if (VITE_OPEN_HEADER_SECURITY == "true" && ["development", "preview"].includes(mode)) {
    if (mode == "preview") {
      csp = {
        "Content-Security-Policy":
          "default-src 'self'; frame-src 'self'; worker-src 'self'; connect-src 'self'; font-src 'self'; img-src 'self'; object-src 'none'; script-src 'self'; style-src 'self';",
      };
    } else if (mode == "development") {
      csp = {
        "Content-Security-Policy":
          "default-src 'self'; frame-src 'self'; worker-src 'self'; connect-src 'self'; font-src 'self'; img-src 'self'; object-src 'none'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
      };
    }
  }
  return {
    devtool: "source-map",
    server: {
      host: "0.0.0.0",
      port: 9527, // 指定开发服务器端口，如果端口已被占用，会自动尝试下一个端口
      open: true,
      headers: csp,
      hmr: {
        overlay: true, // 是否显示一个全屏遮罩层来显示错误信息
      },
      proxy: {
        "/athena": {
          target: VITE_RMS_API_URL,
          changeOrigin: true,
        },
        "/athena-monitor": {
          target: VITE_RMS_API_URL, // 目标 WebSocket 服务地址
          ws: true, // 启用 WebSocket 代理
        },
        "/singleEdit2D": {
          target: "http://localhost:8899/",
          changeOrigin: true,
        },
      },
      cors: true, // 是否允许跨域请求，默认为 false
    },
    optimizeDeps: {
      include: ["element-plus/es/components/**/style/index"],
    }, // 优化vite切换页面速度
    plugins: VITE_VUE_DEV_TOOLS == "true" ? [vueDevTools()] : [],
  };
});
