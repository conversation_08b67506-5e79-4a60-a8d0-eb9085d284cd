import { useEditMap } from "@packages/hook/useEdit";
import { usePureNumber } from "@packages/hook/useRules";
import { NodeAttrEditConf } from "@packages/type/editUiType";

const editMap = useEditMap();

// 电梯新增的配置内容
export const ELEVATOR_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  return {
    name: "elevator",
    tabTitle: "电梯新增",
    labelWidth: "85px",
    formItem: [
      {
        prop: "cellCode",
        label: "lang.rms.fed.coverPoint",
      },
      // 电梯编号
      {
        prop: "elevatorId",
        label: "lang.rms.fed.elevatorNumber",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        rules: [usePureNumber()],
        required: true
      },
      // 排队点
      {
        prop: "queueCellsCodeIdMap",
        label: "lang.rms.fed.ququePoint",
        component: "PointSelect",
        set(value: any[], allData: { [k: string]: any }) {
          allData.queueCellsCodeIdMap = value;
          const queueCells = value.map(item => {
            return item.cellCode;
          });
          allData.queueCells = [queueCells];
          allData.entryQueueNum = queueCells.length;
        },
      },
      // 暂停点
      {
        prop: "entryWaitNum",
        label: "lang.rms.fed.pausePoint",
        component: "elInputNumber",
        describe: "lang.rms.fed.pausePointMsg0",
        min: 0,
        step: 1,
        max: 10000,
      },
    ],
  };
};
