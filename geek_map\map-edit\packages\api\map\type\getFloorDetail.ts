import type {
  MapChargerDto,
  MapNodeDto,
  MapSingleLaneDto,
  MapStationDto,
  MapSegmentDto,
} from "@packages/type/editNode";
export interface MapFloorDto {
  floorId: number;
  id: number;
  mapAdditionalPointDtoList: any[] | null;
  mapAreaList: any[] | null;
  mapMarkerList: any[] | null;
  mapElevatorDtoList: any[] | null;
  dmpDeviceDtoList: any[] | null;
  dmsDeviceDtoList: any[] | null;
  mapChargerDtoList: MapChargerDto[] | null;
  mapNodeDtoList: MapNodeDto[] | null;
  mapSingleLaneDtoList: MapSingleLaneDto[] | null;
  mapStationDtoList: MapStationDto[] | null;
  mapSegmentDtoList: MapSegmentDto[] | null;
}

export interface GetFloorDetailParams {
  mapId: string | number;
  floorId: string | number;
}

export interface GetFloorDetailResult {
  mapFloorDto: MapFloorDto;
  mapId: number;
  mapName: string;
  moveOperationDtos: null;
  status: number;
}
