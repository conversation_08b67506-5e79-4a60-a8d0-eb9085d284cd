<template>
  <!-- x y -->
  <p v-if="curItem.latticeInfos">{{ $t("lang.rms.fed.workbench") }}</p>
  <div class="latticeMain">
    <div
      v-for="row in sortLatticeInfosByLine(curItem.latticeInfos)"
      :key="row[0].line"
      class="latticeBox"
      :style="{ width: '100%' }"
    >
      <template v-for="lattice in row" :key="`${lattice.line}_${lattice.layerColumnNum}`">
        <div class="lattice">
          <div class="lattice-item">
            <div class="workbench">
              <div class="number">
                <span>x:</span>
                <el-input-number
                  class="w80 input-number"
                  size="mini"
                  v-model="lattice.locationX"
                  :controls="false"
                  :disabled="viewDevice"
                  @onChange="locationChange"
                />
              </div>
              <div class="number">
                <span>y:</span>
                <el-input-number
                  class="w80 input-number"
                  size="mini"
                  v-model="lattice.locationY"
                  :controls="false"
                  :disabled="viewDevice"
                  @onChange="locationChange"
                />
              </div>
            </div>
            <div class="switch-button">
              <el-switch
                v-model="lattice.hasBcr"
                size="small"
                class="block"
                :disabled="viewDevice"
                :active-text="$t('lang.rms.fed.ppp.stationModel.bcr')"
                :active-value="1"
                :inactive-value="0"
              />
              <el-switch
                v-model="lattice.hasWeight"
                size="small"
                class="block"
                :disabled="viewDevice"
                :active-text="$t('lang.rms.fed.ppp.stationModel.weight')"
                :active-value="1"
                :inactive-value="0"
              />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  curItem: any;
  viewDevice?: boolean;
  modelType: number; // 0 集货工作站, 1 上检下运工作站, 2. 其他
}>();

const latticeBoxWidth = computed(() => {
  if (props.modelType === 2) {
    return `${props.curItem.latticeInfos.length * 260}px`;
  } else {
    return "100%";
  }
});

const locationChange = () => {
  console.log("curItem::变了么", props.curItem);
};

// 根据 line 和 layerColumnNum 排列成 多行 多列的二维数组
const sortLatticeInfosByLine = (latticeInfos: any) => {
  if (!latticeInfos || latticeInfos.length === 0) {
    return [];
  }

  // 按 line 和 layerColumnNum 排序
  latticeInfos.sort((a, b) => {
    if (a.line !== b.line) {
      return a.line - b.line;
    }
    return (a.layerColumnNum || 1) - (b.layerColumnNum || 1);
  });

  const result = [];
  let currentLine: number = 1;
  let currentRow: any = [];

  latticeInfos.forEach((item: any) => {
    const line = item.line || 1;
    if (line !== currentLine) {
      if (currentRow.length > 0) {
        result.push(currentRow);
      }
      currentRow = [];
      currentLine = line;
    }

    currentRow.push(item);
  });

  if (currentRow.length > 0) {
    result.push(currentRow);
  }

  return result.reverse(); // 反转数组，使得第一行在最上面
};
</script>

<style lang="less" scoped>
.w80 {
  width: 80px;
}

.block {
  display: block;
}

.workbench {
  display: flex;
  flex-direction: column;
  padding: 5px;

  .number {
    flex: 1;
    border-right: none;
    display: flex;
    justify-content: center;
    align-items: center;
    :deep(.el-input__wrapper) {
      padding-right: 1px;
      padding-left: 1px;
    }
    :deep(.el-input-number) {
      width: 60px;
    }
    span {
      margin-right: 5px;
    }
  }
}

.switch-button {
  padding: 5px 0;
  position: relative;
  :deep(.el-switch) {
    margin: 4px;
    height: 16px;
  }
  :deep(.el-switch__core) {
    vertical-align: top;
  }
}

.latticeMain {
  margin-bottom: 20px;
  overflow: auto;

  .latticeBox {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 5px;
    overflow: hidden;
    margin-bottom: 5px;
  }

  .lattice {
    flex: 1;
    .lattice-item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      min-width: 170px;
      height: 60px;
      gap: 10px;
      padding: 5px;
      position: relative;
      border: 1px solid #ebeef5;
    }
  }

  .input-number {
    height: 32px;
    line-height: 32px;
    width: 80px;
  }

  .define-append {
    height: 32px;
    line-height: 32px;
  }
}
</style>
