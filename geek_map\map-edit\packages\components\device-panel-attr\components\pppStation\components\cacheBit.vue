<template>
  <el-checkbox v-if="!isPppLite" v-model="isCacheBit">
    {{ $t("lang.rms.fed.cacheBit") }}
  </el-checkbox>

  <el-form v-if="isCacheBit" label-width="90px" :model="formData">
    <el-form-item :label="$t('lang.rms.fed.cacheBitPosition')">
      <el-select v-model="formData.transferDirection" :placeholder="$t('lang.rms.fed.choose')">
        <el-option
          v-for="item in transferDirectionDict"
          :key="item.value"
          :label="$t(item.label)"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </el-form>
  <BitLatticePreview
    v-if="['UP', 'UP_AND_DOWN'].includes(formData.transferDirection) && isCacheBit"
    ref="upBitLatticeRef"
    :options="upOption"
    :defRowNum="4"
    :defColNum="3"
  />
  <!-- modelType -->
  <LocationXY :modelType="modelType" v-if="options" :cur-item="options" :view-device="viewDevice" />
  <BitLatticePreview
    v-if="['DOWN', 'UP_AND_DOWN'].includes(formData.transferDirection) && isCacheBit"
    ref="downBitLatticeRef"
    :defRowNum="1"
    :defColNum="2"
    :options="downOption"
  />
</template>

<script setup lang="ts">
import { ref, watch, Ref, computed, defineProps } from "vue";
import BitLatticePreview from "./bitLatticePreview.vue";
import LocationXY from "./locationXY.vue";

const props = defineProps<{
  isPppLite: boolean;
  options: any;
  viewDevice: boolean;
  deviceModel: any;
}>();

const isCacheBit = ref(false);

const formData = ref({
  transferDirection: "NONE",
});

const upOption = ref([]);
const downOption = ref([]);

watch(
  () => props.options,
  value => {
    const { transferLatticeInfos, transferDirection, defaultTransferLatticeInfos } = value;
    isCacheBit.value = transferDirection !== "NONE";
    formData.value.transferDirection = transferDirection;
    const upOptionData =
      transferLatticeInfos === null ? defaultTransferLatticeInfos : transferLatticeInfos;

    upOption.value = (upOptionData || []).filter((item: any) => {
      return item?.latticeCode?.startsWith("CA") || "CA";
    });

    downOption.value = (transferLatticeInfos || []).filter((item: any) => {
      return item?.latticeCode?.startsWith("CB") || "CB";
    });
  },
  {
    immediate: true,
  },
);

watch(isCacheBit, value => {
  if (value && formData.value.transferDirection === "NONE") {
    formData.value.transferDirection = "UP";
  }
});

const modelType = computed(() => {
  const modelCode = props.deviceModel.modelCode;
  switch (true) {
    // 集货工作站
    case ["V1.2.1.1", "V1.2.2.1"].includes(modelCode):
      return 0;
    // 上检下运工作站
    case ["V1.2.1.2", "V1.2.2.2"].includes(modelCode):
      return 1;
    default:
      return 2;
  }
});

const upBitLatticeRef = ref(null);
const downBitLatticeRef = ref(null);

const transferDirectionDict = computed(() => {
  return [
    { label: "lang.rms.fed.aboveTheWorkbench", value: "UP" },
    { label: "lang.rms.fed.underTheWorkbench", value: "DOWN" },
    // { label: "lang.rms.fed.aboveBelowWorkbench", value: "UP_AND_DOWN" }, RMSDEV-44876 去掉
  ];
});

function getData(stopId: any) {
  const transferDirection = isCacheBit ? formData.value.transferDirection : "NONE";
  let upBitLatticeData = [];
  let downBitLatticeData = [];
  const transferLatticeInfos = [];

  if (upBitLatticeRef.value) {
    upBitLatticeData = (upBitLatticeRef.value as any)
      .getCacheBitData({ stopId, position: "CA" })
      .map((item: any) => {
        return {
          ...item,
          direction: "UP",
        };
      });
  }

  if (downBitLatticeRef.value) {
    downBitLatticeData = (downBitLatticeRef.value as any)
      .getCacheBitData({ stopId, position: "CB" })
      .map((item: any) => {
        return {
          ...item,
          direction: "DOWN",
        };
      });
  }

  transferLatticeInfos.push(...upBitLatticeData, ...downBitLatticeData);

  return {
    transferDirection,
    transferLatticeInfos,
  };
}

defineExpose({ getData });
</script>

<style lang="less" scoped>
.w100 {
  width: 120px;
}
.w50 {
  width: 50px;
}

.cacheBitPreview {
  overflow: auto;
  width: 100%;
  max-height: 210px;

  .row {
    white-space: nowrap;

    .row-title {
      display: inline-block;
      width: 60px;
      text-align: right;
      margin-right: 5px;
      // css 不换行, 超出省略
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 34px;
      vertical-align: bottom;
    }

    .col {
      display: inline-block;
      width: 130px;
      padding: 5px;
      text-align: center;
      // 禁止换行
      white-space: nowrap;
      border: 1px solid #999;
      border-right: none;
      border-bottom: none;

      // 最后一列, border-right
      &:last-child {
        border-right: 1px solid #999;
      }
    }
  }

  // 最后一行
  .row:last-child {
    .col {
      border-left: none;
      border-right: none;
    }
  }

  :deep(.el-input__wrapper) {
    padding-left: 0px;
    padding-right: 0px;
  }
}
</style>
