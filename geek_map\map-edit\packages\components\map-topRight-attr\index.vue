<template>
  <div class="topRightPanels">
    <el-button
      v-for="item in modelValue"
      v-bind="item"
      :disabled="isGlobalDisabled"
      @click="headelClick(item.eventName)"
      >{{ $t(item.text) }}</el-button
    >
  </div>

  <UploadBg v-model="isUploadBg" />
</template>
<script lang="ts">
export default {
  name: "toolpanel",
};
</script>
<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAttrStore } from "@packages/store/attr";
import { triggerEventListener } from "@packages/hook/useEvent";
import UploadBg from "./components/uploadBg.vue";
import { ref } from "vue";

interface PropsType {
  modelValue: {
    [key: string]: any;
  }[];
}

defineProps<PropsType>();

const { isGlobalDisabled } = storeToRefs(useAttrStore());
const isUploadBg = ref(false);
function headelClick(name: string) {
  if (name === "uploadBg") {
    // 上传背景图单独处理
    isUploadBg.value = true;
  } else {
    triggerEventListener(name);
  }
}
</script>

<style lang="scss">
.topRightPanels {
  float: right;
  position: absolute;
  top: 0px;
  right: 10px;
  height: 33px;
  line-height: 33px;
}
</style>
