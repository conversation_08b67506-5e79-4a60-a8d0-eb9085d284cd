<template>
  <div class="functionItem">
    <span v-if="!isMultipleSelectMode" class="close mapFont map-font-guanbi1" @click="closeItem"></span>
    <div class="title">
      <el-tooltip v-if="funcDesc" effect="dark" :enterable="false" :content="$t(funcDesc)" placement="bottom">
        <span class="mapFont map-font-info describeIcon"></span>
      </el-tooltip>
      {{ $t(funcTitle) }}
    </div>
    <div class="content">
      <BaseForm
        class="funcBaseFrom"
        label-position="top"
        :form-item="fromItem"
        :form-data="fromData"
        @update:form-data="(data: any) => updateFormData(funcName, data)"
      >
        <template #funcRackAngle="{ option }">
          <FuncRackAngle :fromData="option.fromData" :updateValue="option.update" />
        </template>
      </BaseForm>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ComputedRef, toRefs } from "vue";
import { storeToRefs } from "pinia";
import { useAttrStore } from "@packages/store/attr";
import { funcOptions } from "@packages/configure/function.conf";
import { FormItemExPropsType } from "@packages/type/editUiType";
import BaseForm from "@packages/components/map-form/formBase.vue";
import FuncRackAngle from "./func_rackAnglev2.vue";

/*********************
 *      前置数据       *
 *********************/
interface FormDataType {
  [k: string]: any;
}

const props = defineProps<{
  funcName: string;
  fromData: FormDataType;
}>();

const { fromData, funcName } = toRefs(props);

const attrSotreObj = useAttrStore();
const attrStore = storeToRefs(attrSotreObj);

// 接口中的功能节点预设数据
const functionalLabels = attrStore.functionalLabels;

const emits = defineEmits<{
  (event: "close"): void;
  (event: "update", data: any): void;
}>();

/*********************
 *      解析数据       *
 *********************/
// 当前功能区对应接口中的数据
const funcLabel = computed(() => {
  return functionalLabels.value.find(item => item.name === funcName.value);
});

// 当前功能区对应本地配置
const funcConf = computed(() => {
  return funcOptions(fromData.value)[funcName.value];
});

// 当前功能区名称
const funcTitle: ComputedRef<string> = computed(() => {
  return funcLabel.value?.text || "";
});

// 当前功能区是否有describe
const funcDesc: ComputedRef<string> = computed(() => {
  return funcConf.value?.describe || "";
});

/**
 * 当前功能节点的fromItem
 */
const fromItem: ComputedRef<FormItemExPropsType[]> = computed(() => {
  const funItem = funcLabel.value;
  if (funItem) {
    const curFunOptions = funcConf.value || {};
    const funData = Object.keys(funItem.params).map(paramsKey => {
      const paramsTitle = funItem.params[paramsKey]?.lang;
      const paramsConf = curFunOptions[paramsKey] || {
        component: "elInput",
        clearable: true,
      };

      const data = {
        prop: paramsKey,
        label: paramsTitle,
        ...paramsConf,
      };

      if (!paramsConf.appendSlotName) {
        data.component = paramsConf.component || "elInput";
      }

      return data;
    });

    funData.sort((a, b) => {
      return (a.index || a.label) - (b.index || b.label);
    });

    return funData
  }
  return [];
});

const isMultipleSelectMode = computed(() => {
  return attrStore.isMultipleSelectMode.value || attrStore.isSingleSelectMode.value || attrStore.isFnChooseMode.value;
});

/*********************
 *         方法       *
 *********************/
/**
 * 删除一个功能节点
 */
function closeItem() {
  emits("close");
}

function updateFormData(name: string, data: any) {
  emits("update", { name, data });
}
</script>

<style scoped lang="scss">
.functionItem {
  position: relative;
  border: 1px dashed #ccc;
  margin-bottom: 10px;
  padding: 10px;
  .title {
    font-size: 14px;
    font-weight: 900;
  }

  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    height: 16px;
    line-height: 16px;
    cursor: pointer;
  }
}
.funcBaseFrom {
  width: 100%;
}

.describeIcon {
  color: #409eff;
}
</style>
