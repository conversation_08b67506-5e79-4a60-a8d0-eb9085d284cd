/* ! <AUTHOR> at 2025/03/18 */
/**
 * @description
 * 用于查找当前文件夹下所用到的所有接口地址，并将结果写入文件
 * 结果文件路径：./build/node_bash/searchResults.txt
 * 使用方法：在项目根目录下运行：node .\build\node_bash\filterFiles.js
 */

const fs = require("fs");
const path = require("path");

// 设置要搜索的文件夹路径和关键词
const folderPath = "./"; //文件夹路径
const foldersToSkip = ["node_modules", "dist", "build", "geek_map\\map2d", ".git"]; // 需要跳过的文件夹名称
const outputPath = "./build/node_bash/searchResults.txt"; // 输出文件路径

// 用于存储搜索结果
const searchResults = [];

// 递归遍历文件夹
function traverseFolder(folder) {
  const files = fs.readdirSync(folder);

  files.forEach(file => {
    const filePath = path.join(folder, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      const isSkip = foldersToSkip.some(folderName => {
        if (filePath.includes(folderName)) return true;
      });
      if (isSkip) return;
      traverseFolder(filePath);
    } else {
      if (filePath.endsWith(".js") || filePath.endsWith(".vue") || filePath.endsWith(".json")) {
        // 根据需要修改文件类型过滤器
        searchKeywordInFile(filePath);
      }
    }
  });
}

// 在文件中搜索关键词
function searchKeywordInFile(file) {
  const content = fs.readFileSync(file, "utf-8");
  const lines = content.split("\n");

  lines.forEach((line, index) => {
    // 跳过行内容包含.html的行
    if (line.includes(".html")) {
      return;
    }
    const interfaceAddress = extractInterfaceAddress(line);
    if (interfaceAddress.length > 0) {
      console.log(`接口地址: ${interfaceAddress}`);
      console.log("----------------------");
      searchResults.push({
        file: file,
        line: index + 1,
        interfaces: interfaceAddress,
      });
    }
  });
}

// 提取接口地址
function extractInterfaceAddress(line) {
  // const regex = /\/athena[^"]+/g;
  const regex = /\/athena[^"'`]+["`]/g;
  const matches = line.match(regex);
  // return matches || [];
  return matches ? matches.map(match => match.slice(0, -1)) : [];
}

// 将结果写入文件
function writeResultsToFile() {
  let outputContent = "";

  searchResults.forEach(result => {
    outputContent += `文件: ${result.file}\n`;
    outputContent += `行号: ${result.line}\n`;
    outputContent += `接口地址: ${result.interfaces.join(", ")}\n`;
    outputContent += "----------------------\n";
  });

  fs.writeFileSync(outputPath, outputContent);
  console.log(`搜索结果已写入文件: ${outputPath}`);
}

traverseFolder(folderPath);
writeResultsToFile();
