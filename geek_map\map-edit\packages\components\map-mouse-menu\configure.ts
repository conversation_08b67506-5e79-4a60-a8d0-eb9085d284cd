import { MapMouseMenu } from "./main";
import { useMapMouseMenuStore } from "./store";
import { useEditMap } from "@packages/hook/useEdit";
import { NODE_CELL } from "@packages/configure/dict/nodeType";
import { getSelectNodeType } from "@packages/hook/useRightFrom";
import { NODE_IDKEY_MAP } from "@packages/configure/dict/nodeType";
import { useAttrStore } from "@packages/store/attr";
import { useAppStore } from "@packages/store/app";
import {deleteArea} from "@packages/api/map";
const mapMouseMenu = new MapMouseMenu({
  expandTrigger: "hover",
});

const editMap = useEditMap();

// 复制
mapMouseMenu.addMenuItem({
  label: "lang.rms.web.map.version.copy",
  eventName: "menu:copy",
  condition(nodes: any[], layerName: string) {
    return layerName === NODE_CELL;
  },
  handle(nodes: any[], layerName: string) {
    // 进入copy模式
    const mapMouseMenuStore = useMapMouseMenuStore();
    mapMouseMenuStore.copyMode();
    editMap.value?.triggerLayers([]);
  },
});

// 批量编辑
mapMouseMenu.addMenuItem({
  label: "批量编辑功能",
  eventName: "menu:batchEditFun",
  condition(nodes: any[], layerName: string) {
    return nodes.length > 1 && layerName === NODE_CELL;
  },
  handle(nodes: any[], layerName: string) {
    // 进入批量编辑模式
    const mapMouseMenuStore = useMapMouseMenuStore();
    mapMouseMenuStore.batchModifyCellFun();
  },
});

// 批量删除
mapMouseMenu.addMenuItem({
  label: "lang.rms.fed.delete",
  eventName: "menu:batchRemove",
  handle() {
    const attrStore = useAttrStore();
    const appStore = useAppStore()
    // 删除元素
    editMap.value?.deleteElements({
      id: <string>attrStore.layerName,
      data: attrStore.selectNodes
        .map(item => {
          const nodeType = getSelectNodeType(item) || "";
          return (item as any)[NODE_IDKEY_MAP[nodeType]];
        })
        .filter(item => item),
    });
    //调用接口告知区域删除，咱也不知道干啥的
    // if(attrStore.layerName === 'AREA'){
    //   const areaProperty:any = attrStore.selectNodes[0]
    //   const {areaType,areaId} = areaProperty
    //   const {mapId,floorId} = appStore
    //   deleteArea({mapId,floorId,areaType,areaId})
    // }

    // 清空当前选中
    attrStore.clearSelect();
  },
});

export { mapMouseMenu };
