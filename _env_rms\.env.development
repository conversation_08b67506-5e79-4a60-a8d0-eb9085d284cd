# .env.development
# 开发环境下开发者工具配置
VITE_VUE_DEV_TOOLS=false # 是否开启VUE开发者工具，修改后需手动重启服务
VITE_PIXI_DEV_TOOLS=false # 是否开启PIXI开发者工具，修改后自动生效
VITE_OPEN_HEADER_SECURITY=false # 是否开启安全头设置，修改需手动重启服务
VITE_DEV_MODEL3D=false # 是否开启3D模型开发模式，修改后自动生效
VITE_DEV_MAP2D=false # 是否开启2d监控开发模式，修改后自动生效

# 系统名称
VITE_RMS_TITLE=RMS(Dev)

# 后端接口地址
VITE_RMS_API_URL=http://172.16.4.73/ # 复制粘贴到这里的环境，下面的环境不要删
# VITE_RMS_API_URL=http://172.16.6.136/ # 大伟的环境，看运气经常有大地图
# VITE_RMS_API_URL=http://172.16.2.142/ # 王伟测试环境 暂时584
# VITE_RMS_API_URL=http://172.16.11.13/ # pp
# VITE_RMS_API_URL=http://172.16.7.82/ # pp
# VITE_RMS_API_URL=http://172.16.3.99/ # rsp
# VITE_RMS_API_URL=http://172.16.5.25/ # rsp
# VITE_RMS_API_URL=http://172.16.3.74/ # 四项车npm 
# VITE_RMS_API_URL=http://qa.p.geekplus.cc/  # 密码 GeekIOP2023!
# VITE_RMS_API_URL=http://qa.rsp.geekplus.cc/  # 密码 GeekIOP2023!
# VITE_RMS_API_URL=http://qa.pp.geekplus.cc/  # 密码 GeekIOP2023!
# VITE_RMS_API_URL=http://10.200.4.95/ # 王林林
