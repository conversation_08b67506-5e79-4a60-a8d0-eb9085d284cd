<template>
  <el-dialog
    v-model="validateDialog"
    width="350px"
    :close-on-click-modal="false"
    :title="$t('lang.rms.fed.validateSave')"
    center
  >
    <div v-for="(item, i) in validateMapData" :key="i" class="map-edit-cont">
      <div v-for="(val, index) in item" :key="index">
        {{ getI18n(val) }}
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="copy">{{ $t("lang.rms.fed.copyErrorInfo") }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, Ref } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const validateDialog: Ref<boolean> = ref(false);
const leftDialog: Ref<boolean> = ref(false);
const validateMapData: Ref<string[][]> = ref([]);

function copy() {
  const arr: string[] = [];
  if (validateMapData.value) {
    Object.values(validateMapData.value).forEach(item => {
      item.forEach(val => {
        arr.push(getI18n(val));
      });
    });
  }
  const input = document.createElement("input");
  document.body.appendChild(input);
  input.setAttribute("value", arr.join("\r\n"));
  input.select();

  if (document.execCommand("copy")) {
    document.execCommand("copy");
    ElMessage.success(t("lang.rms.fed.copySucceeded"));
  } else {
    ElMessage.success(t("复制失败！"));
  }

  document.body.removeChild(input);
}

function handleNarrow() {
  validateDialog.value = false;
  leftDialog.value = true;
}

function handleExpand() {
  validateDialog.value = true;
  leftDialog.value = false;
}

function handleClose() {
  leftDialog.value = false;
}

function tootip(textData: string[][]) {
  validateMapData.value = textData;
  validateDialog.value = true;
}

function getI18n(value: string) {
  const [text, ...params] = value.split("|");
  return t(text, params);
}

defineExpose({
  handleNarrow,
  handleExpand,
  handleClose,
  tootip,
  copy,
});
</script>

<style lang="scss" scoped>
.map-edit-cont {
  color: #f56c6c;
  font-size: 14px;
  font-weight: 900;
  margin-bottom: 10px;
}

.dialog-footer {
  margin-top: 30px;
  text-align: center;
  width: 100%;
  display: block;
}
</style>
