import { usePureNumber, useCodeQualified } from "@packages/hook/useRules";
import { NodeAttrEditConf } from "@packages/type/editUiType";
import DICT from "@packages/configure/dict";
// 工作站新增的配置内容
export const STATION_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  attrStore.getStationTypeByOnly();
  return {
    name: "station",
    tabTitle: "lang.rms.api.station.standardPoint",
    labelWidth: "102px",
    formItem: [
      // 附着节点
      {
        prop: "cellCode",
        label: "lang.rms.fed.coverPoint",
      },

      // 启用状态
      // {
      //   prop: "enable",
      //   label: "lang.venus.web.common.enableStatus",
      //   component: "elSelect",
      //   data: DICT.ENABLE_STATUS_DICT || [],
      // },
      // 高度
      {
        prop: "heights",
        label: "lang.rms.palletPositionManage.height",
        component: "elInput",
        describe: "lang.rms.fed.deviceEditingMsg0",
        precision: 2,
        max: 900000,
        min: 0,
        get(fromData: { [k: string]: any }) {
          return (fromData.heights || []).join(",");
        },
        set(value: string, fromData: { [k: string]: any }) {
          fromData.heights = value ? value.split(",") : [];
        },
      },

      // 工作方向
      {
        prop: "direction",
        label: "lang.rms.fed.workDire",
        component: "elSelect",
        data: DICT.STATION_DIRECTION_DICT || [],
      },
      // 类型
      {
        prop: "type",
        label: "lang.rms.fed.type",
        component: "elSelect",
        data: attrStore.stationTypeList || [],
        required: true,
        get(fromData: { [k: string]: any }) {
          return String(fromData.type || "");
        },
        set(value: string, fromData: { [k: string]: any }) {
          fromData.type = String(value || "");
        },
      },
      // 工作站ID
      {
        prop: "stationId",
        label: "lang.rms.web.station.stationId",
        component: "elInput",
        rules: [usePureNumber()],
        maxlength: 9,
        showWordLimit: true,
        required: true,
      },
      // 外部编码
      {
        prop: "hostCode",
        label: "lang.rms.fed.hostCode",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        rules: [useCodeQualified()],
      },
      // 最大排队数量
      // {
      //   prop: "queueSize",
      //   label: "lang.rms.fed.maxQueueNumber",
      //   component: "elInputNumber",
      //   min: 0,
      //   max: 1000,
      //   step: 1,
      // },
      // 排队区控制
      {
        prop: "waitCellNumber",
        label: "lang.rms.fed.queueArea",
        describe: "lang.rms.fed.queueInfo",
        component: "elInputNumber",
        min: 0,
        max: 1000,
        step: 1,
      },
      {
        prop: "deliverCells",
        label: "接驳点码值",
        component: "elInput",
        appendAttrsFn(value: string, data: any) {
          const condition = data.type === DICT.STATION_TYPE_DICT_ROLLER;
          return { condition };
        },
      },
    ],
  };
};
