#!/bin/sh
# 获取当前触发的分支和commit
echo ">>>>>>>>>>>>>>> 当前git分支：${BRANCH}"
echo ">>>>>>>>>>>>>>> 当前git commit text：${COMMIT_TEXT}"
echo ">>>>>>>>>>>>>>> 当前构建触发用户：${AUTH}"

# sandbox相关的分支不打包
if [[ "${BRANCH}" =~ "sandbox" ]]
then
   echo "*************** ${BRANCH}新的分支管理，没有sandbox分支了"
   exit 1
fi

if [ -z "${AUTH}" ]
then
  echo "*************** 打包触发用户不能为空！"
  exit 1
fi

# 对athena_fe分支进行对应
function resolve_branch_athena_fe(){
  if [[ "${BRANCH}" =~ "project" ]]
  then
    echo "athena-fe-${BRANCH#*project-}-$(echo "$BRANCH" | sed 's/^v\([^ -]*\)-project.*/\1/')"
  elif [[ "${BRANCH}" =~ "hotfix" ]]
  then
    echo "athena-fe-hotfix-$(echo "$BRANCH" | sed 's/^.*hotfix-//')"
  elif [[ "${BRANCH}" =~ "feature" ]]
  then
    echo "athena-fe-feature-$(echo "$BRANCH" | sed 's/^v\([^ -]*\)-feature.*/\1/')-${BRANCH#*feature-}"
  elif [[ "${BRANCH}" =~ "bugfix" ]]
  then
    echo "athena-fe-bugfix-$(echo "$BRANCH" | sed 's/^v\([^ -]*\)-bugfix.*/\1/')-${BRANCH#*bugfix-}"
  elif [[ "${BRANCH}" =~ "stable" ]]
  then
    echo "athena-fe-stable-$(echo "$BRANCH" | sed 's/^v\([^ -]*\)-stable.*/\1/')"
  elif [[ "${BRANCH}" =~ "v5.6.0" ]]
  then
    echo "athena-fe-test-${BRANCH: 1}"
  else
     exit 1
  fi 
}

athena_fe_branchName=$(resolve_branch_athena_fe)
if [ ! -n "${athena_fe_branchName}" ]
then
  echo "*************** ${athena_fe_branchName}不符合分支规范！不打包！！！"
  exit 1
fi
echo ">>>>>>>>>>>>>>> athena-fe 需要打包的branchName为 ${athena_fe_branchName}"

# 判断是否需要执行build，commit text 不匹配则退出
git_commit_text=${COMMIT_TEXT: 0: 13}
if [ "${git_commit_text}" != "JENKINS_BUILD" ]
then
  echo "*************** commit text 不符合build规范"
  echo "*************** commit text 不匹配 /^JENKINS_BUILD/"
  echo "*************** 退出执行指令"
  exit 1
fi

rms_path="rms-fe"
athena_path="athena-fe"
rm -rf ${rms_path}
rm -rf ${athena_path}

# 通知微信机器人
# 传参 $1 机器人发送内容
function wx_robot(){
  #wx_robot_url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dddf9a8f-8384-42ee-b190-01e60ee5e8a5 
  wx_robot_url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da145354-f600-4cee-86a5-083a9407c138
  wx_robot_message=`echo ${COMMIT_TEXT} | sed 's/[[:space:]]/./g'`
  wx_tz_user=`echo ${AUTH} | sed 's/[[:space:]]/./g'`
  curl ${wx_robot_url} \
   -H 'Content-Type: application/json' \
   -d '
   {
      "msgtype": "markdown",
      "markdown": {
      	"content": "<font color=\"warning\">'${JOB_NAME}'</font>：'$1' \n> 分支：`'${BRANCH}'` \n触发用户：<font color=\"info\">'${wx_tz_user//./}'</font> \n> 提交信息: <font color=\"comment\">'${wx_robot_message}'</font> \n> 查看详情: ['${BUILD_TAG}']('${BUILD_URL}'/console)"
      }
	}'
}
# 准备环境变量
function init_build_env() {
  export NVM_DIR="/var/lib/jenkins/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
  [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
  
  # 获取当前触发的分支和commit
  echo ">>>>>>>>>>>>>>> 当前git分支：${BRANCH}"
  echo ">>>>>>>>>>>>>>> 当前git commit text：${COMMIT_TEXT}"
  echo ">>>>>>>>>>>>>>> 发布环境列表：${Enviroment}"
  
  source /etc/profile
  echo $NVM_DIR
  echo ">>>>>>>>>>>>>>> node版本："
  node -v
  nvm use 20
  node -v
  
  echo ">>>>>>>>>>>>>>> git版本："
  git --version
  git config -l
  npm config set registry https://repo.geekplus.com/artifactory/api/npm/npm-virtual/
  npm config get registry
}
# rms-fe 代码拉取
function rms_fe_clone(){
  local rms_url="***********************:rms-fed/rms-5.6.git"

  cd ${WORKSPACE}
  echo ">>>>>>>>>>>>>>>开始源代码拉取； path: ${rms_path}； branch: ${BRANCH}"
  git clone -b ${BRANCH} --single-branch --depth 2 ${rms_url} ${rms_path}
  pwd
}
# rms-fe 构建打包
function rms_fe_build(){
  # 地图编辑
  echo ">>>>>>>>>>>>>>> 开始 ${edit2d_path} install"
  local edit2d_path="geek_map/map-edit"
  cd ${WORKSPACE}/${rms_path}/${edit2d_path}
  pwd
  npm install
  echo ">>>>>>>>>>>>>>> 完成 ${edit2d_path} install"
  
  # 兼容一下之前的ts-map-fe
  local ts_monitor2d_path="geek_map/ts-map-fe"
  if [ -d ${WORKSPACE}/${rms_path}/${ts_monitor2d_path} ]
  then 
    cd ${WORKSPACE}/${rms_path}/${ts_monitor2d_path}
    pwd
    npm install
  else
    echo "*************** 这个分支没有ts-map-fe"
  fi
  
  # 项目主目录
  echo ">>>>>>>>>>>>>>> 开始 ${rms_path} npm intall"
  cd ${WORKSPACE}/${rms_path}
  pwd
  npm install
  echo ">>>>>>>>>>>>>>> 完成 ${rms_path} intall"
  
  # 开始build
  echo ">>>>>>>>>>>>>>> build 开始打包"
  npm run jenkins:build
  
  # 判断打包是否成功
  if [ -d "./dist" ]
  then 
    echo ">>>>>>>>>>>>>>> 打包成功"
  else
    echo "*************** 打包失败!!!请项目管理员确认!"
    wx_robot "build失败！！！请项目管理员确认！"  
    exit 1
  fi
}

# athena_fe 对打包文件进行操作
function athena_fe_push(){
  echo ">>>>>>>>>>>>>>> athena-fe 需要打包的分支为 ${athena_fe_branchName}"
  local athena_url="***********************:system_rms/athena-fe.git"
  local rms_package="package.json"
  local rms_package_lock="package-lock.json"
  local scan_path="vuls-scan"
  
  cd ${WORKSPACE}
  # 检查远程分支
  echo ">>>>>>>>>>>>>>>检查远程分支: ${athena_fe_branchName} 是否存在 from ${athena_url}"
  local result=$(git ls-remote -h ${athena_url} refs/heads/${athena_fe_branchName})
  echo $result
  
  # clone分支
  if [[ -n $result ]]
  then
    echo ">>>>>>>>>>>>>已找到远程分支: ${athena_fe_branchName}, 开始源代码拉取path: ${athena_path}"
  	git clone -b ${athena_fe_branchName} --single-branch --depth 2 ${athena_url} ${athena_path}
    cd ${WORKSPACE}/${athena_path}
  else
  	echo ">>>>>>>>>>>>>未找到远程分支: ${athena_fe_branchName}, 克隆默认分支到本地"
    git clone --single-branch --depth 2 ${athena_url} ${athena_path}
    cd ${WORKSPACE}/${athena_path}
    echo ">>>>>>>>>>>>>创建新分支: ${athena_fe_branchName}"
    git checkout -b ${athena_fe_branchName}
  fi
  
  git branch
  
  # 删除之前代码
  echo ">>>>>>>>>>>>>>> 删除之前的打包代码" 
  rm -rf ${WORKSPACE}/${athena_path}/rms_build
  rm -rf ${WORKSPACE}/${athena_path}/${rms_package}
  rm -rf ${WORKSPACE}/${athena_path}/${rms_package_lock}
  rm -rf ${WORKSPACE}/${athena_path}/${scan_path}
  echo ">>>>>>>>>>>>>>> 移动打包后的文件夹到${athena_path}"
  mv ${WORKSPACE}/${rms_path}/dist ${WORKSPACE}/${athena_path}/rms_build
  mv ${WORKSPACE}/${rms_path}/${rms_package} ${WORKSPACE}/${athena_path}/${rms_package}
  mv ${WORKSPACE}/${rms_path}/${rms_package_lock} ${WORKSPACE}/${athena_path}/${rms_package_lock}
  
  # 对代码进行提交
  echo ">>>>>>>>>>>>>>> 对代码进行提交: git add,commit,push" 
  ls -lh
  git add --all >/dev/null
  git commit -m "${AUTH}::${COMMIT_TEXT#*JENKINS_BUILD}" >/dev/null
  git push origin ${athena_fe_branchName}
  
  if [ $? -eq 0 ]
  then
  	echo ">>>>>>>>>>>>>>> push ${athena_fe_branchName} 成功！" 
    echo ">>>>>>>>>>>>>>> 整个构建流程OK！" 
    echo ">>>>>>>>>>>>>>> 构建结束" 
    wx_robot "构建成功：${athena_fe_branchName}"
  else
    echo "*************** 构建提交失败!!!"
  	wx_robot "构建失败！！！请项目管理员确认！"  
    exit 1
  fi
}
wx_robot "构建开始！！！"

# test
# git ls-remote -h ***********************:system_rms/athena-fe.git refs/heads/athena-fe-stable-5.8.7

init_build_env
rms_fe_clone
rms_fe_build
athena_fe_push
