<template>
  <div>
    <div v-show="!operation" class="component-btn-group">
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfUpdate')"
        size="small"
        type="primary"
        @click="controlHandler('update')"
      >
        {{ $t("lang.rms.fed.updateShelf") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfMove')"
        size="small"
        type="primary"
        @click="controlHandler('move')"
      >
        {{ $t("lang.rms.fed.moveTypeShelf") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfUpdateAngel')"
        size="small"
        type="primary"
        @click="controlHandler('updateAngel')"
      >
        {{ $t("lang.rms.fed.updateShelfAngle") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfLock')"
        v-show="shelfData?.lockedState != 'LOCKED'"
        size="small"
        type="primary"
        @click="controlHandler('lock')"
      >
        {{ $t("lang.rms.fed.lockShelf") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfLock')"
        v-show="shelfData?.lockedState == 'LOCKED'"
        size="small"
        type="primary"
        class="dark"
        @click="controlHandler('unlock')"
      >
        {{ $t("lang.rms.fed.unlockShelf") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.shelfReturnPlacement')"
        size="small"
        type="primary"
        @click="controlHandler('placement')"
      >
        {{ $t("lang.rms.fed.shelfReturnPlacement") }}
      </el-button>
    </div>
    <!-- 这是更新的货架的表单 -->

    <shelf-update
      v-if="operation === 'update'"
      :cellCode="cellCode"
      :shelfAngel="shelfData?.angle"
      @updateCellCode="val => (cellCode = val)"
      @cancel="operation = undefined"
    />

    <!-- <shelf-move
      v-if="operation === 'move'"
      :cellCode="cellCode"
      :cellType="cellType"
      @updateCellCode="val => (cellCode = val)"
      @cancel="operation = undefined"
    /> -->

    <shelf-update-angle
      v-if="operation === 'updateAngel'"
      :shelfAngel="shelfData?.angle"
      @cancel="operation = undefined"
    />
     <!-- 详情的表单 -->
    <detail v-if="shelfData" :data="shelfData" />
  </div>
</template>

<script>
import { mapState, mapActions } from "pinia";
import { useMap2dStore } from "@map2d/dataStore/index";
import { useMap2dContainerStore } from "@map2d/dataStore/right-panel/container";
import { useRootMenuStore } from "@stores/rootMenuStore";
import { getMap2D } from "@map2d/singleton";

import ShelfUpdate from "./shelf-update.vue";
import ShelfMove from "./shelf-move.vue";
import ShelfUpdateAngle from "./shelf-update-angle.vue";
import Detail from "./shelf-detail.vue";

export default {
  name: "map-container-shelf",
  components: { Detail, ShelfUpdate, ShelfMove, ShelfUpdateAngle },
  data() {
    return {
      shelfData: null,

      cellCode: "",
      cellType: "",
      operation: undefined, // update | move | updateAngel
    };
  },
  computed: {
    ...mapState(useMap2dStore, ["mapClickData", "wsQueryTimer"]),
    ...mapState(useMap2dContainerStore, ["containerCode"]),
    ...mapState(useRootMenuStore, ["getBtnPermission"]),
  },
  watch: {
    mapClickData: {
      handler(data, oldData) {
        if (!oldData && !data) return;
        const layer = data?.layer || "";
        if (!["shelf", "cell"].includes(layer)) {
          this.clearData();
          this.setContainerCode("");
          return;
        }

        const code = data.code.toString();
        switch (layer) {
          case "shelf":
            if (this.containerCode !== code) {
              this.setContainerCode(code);
            }
            break;
          case "cell":
            if (code !== this.cellCode) {
              this.cellCode = code;
              this.cellType = data?.options?.cellType || ""; //单元格类型
            }
            break;
        }
      },
      immediate: true,
    },

    containerCode: {
      handler(code) {
        this.clearData();
        if (code) {
          this.queryShelfData();
        }
      },
      immediate: true,
    },

    operation(val, oldVal) {
      this.cellCode = "";
      this.cellType = "";
      this.setRightOccupyState(!!val);

      switch (oldVal) {
        case "update":
        case "move":
          const code = this.containerCode;
          const { mapRender } = getMap2D();
          if (code) mapRender.select({ shelf: [code] });
          else mapRender.clearSelects();
          break;
      }
    },
  },
  beforeUnmount() {
    this.clearData();

    Object.assign(this.$data, this.$options.data());
  },
  methods: {
    ...mapActions(useMap2dStore, ["setRightOccupyState", "setWsQueryTimer"]),
    ...mapActions(useMap2dContainerStore, ["setContainerCode", "setSearchData"]),

    clearData() {
      this.setWsQueryTimer(null);
      this.shelfData = null;
      this.cellCode = "";
      this.cellType = "";
      this.setSearchData(null);
      const { mapRender } = getMap2D();
      mapRender?.renderBiz("placement", null, false);
    },
    controlHandler(cmd) {
      const shelfCode = this.containerCode;
      if (!shelfCode) {
        const msg = this.$t("lang.rms.fed.pleaseEnterTheShelfID"); // 机器人编号不能为空
        this.$error(msg);
        return;
      }
      let instruction = "";
      switch (cmd) {
        case "move": // 移动
        case "update": // 更新
        case "updateAngel": // 更新角度
          this.operation = cmd;
          return;
        case "lock": // 锁定
          instruction = "LOCK_SHELF";
          break;
        case "unlock": // 解锁
          instruction = "UNLOCK_SHELF";
          break;
        case "placement": // 回老家
          instruction = "RETURN_SHELF_PLACEMENT";
          break;
      }
      this.operation = undefined;

      const map2D = getMap2D();
      map2D.mapWorker
        .wsRequest("ShelfInstructionRequestMsg", {
          instruction,
          shelfCode,
        })
        .then(res => {
          const msg = $utils.Tools.transMsgLang(res?.header?.msg || "");
          if (res?.header?.code === 0) this.$success(msg);
          else this.$error(msg);
        });
    },

    queryShelfData() {
      this.setWsQueryTimer(null);

      const code = this.containerCode;
      const { mapWorker, mapRender } = getMap2D();
      mapWorker.wsDataQuery("SHELF", code).then(res => {
        if (res?.header?.code !== 0) {
          this.$error(this.$t(res?.header?.msg));
          return;
        }

        const data = res?.body;
        if (!data) {
          this.clearData();
          this.setSearchData(null);
          this.setContainerCode("");
          return;
        }
        data.shelfCode = code;

        this.shelfData = data;
        mapRender?.renderBiz("placement", data, false);

        const _timer = setTimeout(() => {
          this.queryShelfData();
        }, 300);
        this.setWsQueryTimer(_timer);
      });
    },
  },
};
</script>
