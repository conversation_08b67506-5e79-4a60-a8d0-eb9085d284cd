/* ! <AUTHOR> at 2024/10/31 */
// 基础配置
import { resolve } from "path";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import AutoImport from "unplugin-auto-import/vite";
import GeekplusPlusResolver from "geekplus-ui/unplugin/component-resolver";

import aliasConfig from "./vite.alias.config";
import { getVersion } from "./pre_build/index.js";
export default defineConfig(async mode => {
  await getVersion(mode); // 自动获取git版本信息

  return {
    base: "/",
    envDir: resolve("./_env_rms"),
    define: {
      // 用于定义全局常量
      __RMS_IS_DEV__: mode == "development",
      __RMS_TIMESTAMP__: JSON.stringify(Date.now()),
    },
    optimizeDeps: {
      exclude: ["@geek_map/model3d/devLibs/geek-model3d.mjs", "@geek_map/map2d/libs/map2d.js"], // 将要忽略的依赖项的名称
    },
    resolve: {
      extensions: [".js", ".jsx", ".vue", ".ts", ".css", ".less", ".scss", ".json"],
      alias: aliasConfig,
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
          additionalData: `@use "@sass/index.scss" as *;`, // 样式文件主入口
          quietDeps: true, // 忽略来自依赖的警告
        },
        // 用于配置 CSS 预处理器 less 的全局变量。
        less: {
          additionalData: "@import '@less/mixins/index.less';",
          javascriptEnabled: true,
        },
      },
    },
    assetsInclude: [
      "**/*.glb", // 假设你想要包含所有 .glb (3D模型) 文件作为资源
    ],
    plugins: [
      vue(),
      vueJsx(),
      AutoImport({
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass", // 使用 sass 导入样式
          }),
        ],
      }),
      Components({
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass", // 使用 sass 导入样式
          }),
          GeekplusPlusResolver(),
        ],
      }),
    ],
  };
});
