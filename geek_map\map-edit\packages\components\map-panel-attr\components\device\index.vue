<template>
  <div class="attrPanel">
    <TabFormBase
      v-if="curSelectNode"
      ref="baseTabsRef"
      :fromData="curSelectNode"
      :title="$t(title)"
      :tabs="tabData"
      @change="change"
    >
      <template #pinpointSlot>
        <Pinpoint />
      </template>
    </TabFormBase>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, ComputedRef, ref, watch } from "vue";
import TabFormBase from "@packages/components/map-form/tabBase.vue";
import { useAttrStore } from "@packages/store/attr";
import { getTabConf, changeBaseTabData } from "../common";
import { NODE_DEVICE } from "@packages/configure/dict/nodeType";
import Pinpoint from "./components/pinpoint.vue";

const attrStore = useAttrStore();
const attrStoreRef = storeToRefs(useAttrStore());

const props = defineProps<{
  panelConf?: {
    hiddenTabs: string[];
    hiddenCodes: string[];
    disabledCodes: string[];
  };
  title: string;
}>();

const hiddenTabs: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenTabs || []);
const hiddenCodes: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenCodes || []);
const disabledCodes: ComputedRef<string[]> = computed(() => props.panelConf?.disabledCodes || []);
const baseTabsRef = ref();

const tabData = computed(() => {
  return getTabConf(
    {
      hiddenTabs: hiddenTabs.value,
      hiddenCodes: hiddenCodes.value,
      disabledCodes: disabledCodes.value,
      type: NODE_DEVICE,
    },
    attrStore,
    baseTabsRef.value,
  );
});

const curSelectNode = ref(attrStoreRef.curNodeDataByIndex.value);

watch(
  attrStoreRef.curNodeDataByIndex,
  value => {
    baseTabsRef.value && baseTabsRef.value.setItemAll(value);
  },
  {
    deep: true,
    immediate: true,
  },
);

function change(option: any) {
  changeBaseTabData(option, attrStore);
}

function getRef() {
  return baseTabsRef.value;
}

defineExpose({
  getRef,
});
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 0px;
}
</style>
