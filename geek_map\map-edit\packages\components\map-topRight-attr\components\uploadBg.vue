<template>
  <el-dialog title="上传背景图" @close="close">
    <el-upload drag action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" :limit="1">
      <gp-icon name="gp-icon-upload"><upload-filled /></gp-icon>
      <div class="el-upload__text">拖拽文件或 <em>点击此处上传</em></div>
    </el-upload>
  </el-dialog>
</template>
<script lang="ts">
export default {
  name: "toolpanel",
};
</script>
<script setup lang="ts">
interface PropsType {
  modelValue: boolean;
}

defineProps<PropsType>();

const emits = defineEmits<{
  (event: "update:modelValue", data: boolean): void;
}>();

function close() {
  emits("update:modelValue", false);
}
</script>

<style lang="scss">
.topRightPanels {
  float: right;
  position: absolute;
  top: 0px;
  right: 10px;
  height: 33px;
  line-height: 33px;
}
</style>
