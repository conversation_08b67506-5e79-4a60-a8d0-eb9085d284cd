<template>
  <div class="bottom-info-con">
    <div class="info-item" v-for="(item,index) in infoArr">
      <span class="label">{{$t(item.label)}}：</span>
      <span class="value">{{item.value}}</span>
    </div>
  </div>
</template>

<script setup>
import {onMounted,computed} from 'vue'
import { useTemplateStore } from "@packages/store/template";
const templateStore = useTemplateStore()
const infoArr = computed(() => {
  const mapId = templateStore.mapId
  const mapName = templateStore.mapName
  const floorId = templateStore.floorId
  const arr = [
    {label:'lang.rms.fed.mapName',value:mapName},
    // {label:'版本名称',value:1231241231231241231231},
    {label:'lang.rms.fed.mapID',value:mapId},
    {label:'lang.rms.fed.floor',value:floorId},
  ]
  return arr
})
const mapId = computed(() => {
  return templateStore.mapId
})
const mapName = computed(() => {
  return templateStore.mapName
})
const floorId = computed(() => {
  return templateStore.floorId
})
onMounted(() => {
  const {mapName,mapId,floorId} = templateStore
  console.log(mapName,mapId,floorId,'tttttt')
})
</script>

<style scoped lang="scss">
.bottom-info-con{
  position: absolute;
  width: 100%;
  height: 30px;
  line-height: 30px;
  left: 0;
  bottom: 0;
  box-shadow: 0 3px 5px 1px #eee;
  background-color: #fff;
  font-size: 14px;
  z-index: 2;
  display: flex;
  padding-left: 40px;
  .info-item{
    //flex: 0 0 200px;
    min-width: 200px;
    //font-weight: bolder;
    .label{
      color: #808080;
    }
    .value{
      font-weight: bolder;
    }
  }
}
</style>
