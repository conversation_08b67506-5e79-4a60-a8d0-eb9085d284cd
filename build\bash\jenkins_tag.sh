#!/bin/sh
# 获取当前触发的分支和commit
echo ">>>>>>>>>>>>>>> tag 分支：${BRANCH}"
echo ">>>>>>>>>>>>>>> tag 名称：${TAG_NAME}"
echo ">>>>>>>>>>>>>>> 当前触发用户：${AUTH}"
echo ">>>>>>>>>>>>>>> tag提交信息：${COMMIT}"

# sandbox相关的分支不打包
if [[ "${BRANCH}" =~ "sandbox" ]] || [[ "${BRANCH}" =~ "feature" ]] || [[ "${BRANCH}" =~ "bugfix" ]]
then
   echo "*************** ${BRANCH}这些分支不允许打tag！！！"
   exit 1
fi

if [ -z "${AUTH}" ]
then
  echo "*************** 打包触发用户不能为空！"
  exit 1
fi

# 通知微信机器人
# 传参 $1 机器人发送内容
function wx_robot(){
  #wx_robot_url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dddf9a8f-8384-42ee-b190-01e60ee5e8a5 
  wx_robot_url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da145354-f600-4cee-86a5-083a9407c138
  wx_tz_user=`echo ${AUTH} | sed 's/[[:space:]]/./g'`
  curl ${wx_robot_url} \
   -H 'Content-Type: application/json' \
   -d '
   {
      "msgtype": "markdown",
      "markdown": {
      	"content": "<font color=\"warning\">'${JOB_NAME}'</font>：'$1' \n> 分支：`'${BRANCH}'` \n触发用户：<font color=\"info\">'${wx_tz_user//./}'</font> \n> tag名称: <font color=\"comment\">'${TAG_NAME}'</font> \n> 查看详情: ['${BUILD_TAG}']('${BUILD_URL}'/console)"
      }
	}'
}

athena_path="athena-fe"
rm -rf ${athena_path}

# 准备环境变量
function init_build_env() { 
  source /etc/profile
  
  echo ">>>>>>>>>>>>>>> git版本："
  git --version
  git config -l
}

# athena_fe 对打包文件进行操作
function athena_fe_tag(){
  echo ">>>>>>>>>>>>>>> athena-fe tag分支为 ${BRANCH}"
  local athena_url="***********************:system_rms/athena-fe.git"
  
  cd ${WORKSPACE}
  # 检查远程分支
  echo ">>>>>>>>>>>>>>>检查远程分支: ${BRANCH} 是否存在 from ${athena_url}"
  local result=$(git ls-remote -h ${athena_url} refs/heads/${BRANCH})
  echo $result
  
  # clone分支
  if [[ -n $result ]]
  then
  	git clone -b ${BRANCH} --single-branch --depth 2 ${athena_url} ${athena_path}
    cd ${WORKSPACE}/${athena_path}
    git branch
  else 
   echo "*************** 未找到远程分支：${BRANCH}，不能够打tag！！！"
   exit 1
  fi
  
  # 检查远程仓库中是否已存在该tag
  if git ls-remote --refs origin | grep -q "refs/tags/$TAG_NAME"; then
    echo "*************** tag已存在，请更换tag名称: $TAG_NAME"
    exit 1
  fi
  
  # 定义文件路径
  JSON_FILE="./rms_build/static/configs/version.config.json"
  # 检查文件是否存在
  if [ ! -f "$JSON_FILE" ]
  then
    echo "*************** 找不到文件: $JSON_FILE"
    # 打tag
    echo ">>>>>>>>>>>>>>> 打tag"
    git tag -a ${TAG_NAME} -m "tag ${TAG_NAME}"
    echo ">>>>>>>>>>>>>>> push tag"
    git push origin ${TAG_NAME}
    wx_robot "tag已发布：${TAG_NAME}；ps：可能是老版本，没有version.config文件，请确认！"
  else
  	# 读取 tag 值并保存为 origin_tag_name
    local origin_tag_name=$(grep '"tag":' "$JSON_FILE" | sed -e 's/.*"tag": "\(.*\)"/\1/')
    # 打印原始 tag 值
    echo "Original tag name: $origin_tag_name"
    
    # 将 tag 的值复制为 ${TAG_NAME} 变量
    sed -i "s/\"tag\": \"$origin_tag_name\"/\"tag\": \"$TAG_NAME\"/g" "$JSON_FILE"
    
    # 提交到 git 库
    git add "$JSON_FILE"
    git commit -m "用户:${AUTH}; 新增tag:${TAG_NAME}; ${COMMIT}"
    
    # 打tag
    echo ">>>>>>>>>>>>>>> 打tag"
    git tag -a ${TAG_NAME} -m "tag ${TAG_NAME}"
    echo ">>>>>>>>>>>>>>> push tag"
    git push origin ${TAG_NAME}
    
    # 之后再把这个文件的 tag 值还原为 origin_tag_name
    sed -i "s/\"tag\": \"$TAG_NAME\"/\"tag\": \"$origin_tag_name\"/g" "$JSON_FILE"
    # 提交一次
    git add "$JSON_FILE"
    git commit -m "reset original tag name"
    git push origin ${BRANCH} 
    
    wx_robot "tag已发布：${TAG_NAME}"
  fi
  
  if [ $? -eq 0 ]
  then
  	echo ">>>>>>>>>>>>>>> 打tag成功！"
  else
    echo "***************打tag失败!!!"
    wx_robot "打tag失败：${TAG_NAME}！请发布者确认！"
    exit 1
  fi
}

init_build_env
athena_fe_tag