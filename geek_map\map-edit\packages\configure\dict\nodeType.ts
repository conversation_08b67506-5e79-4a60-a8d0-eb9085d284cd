/**
 * 这里存储了所有的节点类型的数据
 */

/*************************************
 *        地图中所有元素的类型           *
 *************************************/
/**
 * 单元格, 包含普通单元格, 二维码单元格, 货架单元格等
 */
export const NODE_CELL = "CELL";
/**
 * 线, 包含直线, 曲线, 顺时针弧线, 逆时针弧线等
 */
export const NODE_LINE = "LINE";
/**
 * 设备, 包含工作站, 充电站, 反光柱等
 */
export const NODE_DEVICE = "DEVICE";
/**
 * 区域, 包含十字路口区域,系统急停区域等
 */
export const NODE_AREA = "AREA";

/*************************************
 *     地图中所有元素的类型对应的唯一值    *
 *************************************/

/**
 * 单元格对应的唯一ID键
 */
export const NODE_CELL_IDKEY = "nodeId";

/**
 * 线对应的唯一ID键
 */
export const NODE_LINE_IDKEY = "segmentId";

/**
 * 设备对应的唯一ID键
 */
export const NODE_DEVICE_IDKEY = "id";

/**
 * 区域对应的唯一ID键
 */
export const NODE_AREA_IDKEY = "id";

/**
 * 元素类型对应ID键对照表
 */
export const NODE_IDKEY_MAP: { [k: string]: string } = {
  [NODE_CELL]: NODE_CELL_IDKEY,
  [NODE_LINE]: NODE_LINE_IDKEY,
  [NODE_DEVICE]: NODE_DEVICE_IDKEY,
  [NODE_AREA]: NODE_AREA_IDKEY,
};

/**
 * 元素类型对应类型字段对照表
 */
export const NODE_TYPEKEY_MAP: { [k: string]: string } = {
  [NODE_CELL]: "cellType",
  [NODE_LINE]: "segmentType",
  [NODE_DEVICE]: "deviceType",
  [NODE_AREA]: "areaType",
};

/*************************************
 *       地图中所有单元格的类型          *
 *************************************/

/**
 * 单元格/二维码单元格
 */
export const OMNI_DIR_CELL = "OMNI_DIR_CELL";
/**
 * 单元格 - 货架
 */
export const SHELF_CELL = "SHELF_CELL";
/**
 * 单元格 - 工作站
 */
export const STATION_CELL = "STATION_CELL";
/**
 * 单元格 - 充电站
 */
export const CHARGER_CELL = "CHARGER_CELL";
/**
 * 单元格 - 阻塞点
 */
export const BLOCKED_CELL = "BLOCKED_CELL";
/**
 * 单元格 - 托盘架点
 */
export const PALLET_RACK_CELL = "PALLET_RACK_CELL";
/**
 * 单元格 - 过道
 */
export const DROP_CELL = "DROP_CELL";
/**
 * 单元格 - 电梯
 */
export const ELEVATOR_CELL = "ELEVATOR_CELL";
/**
 * 单元格 - 二维码单元格
 */
export const QUEUE_CELL = "QUEUE_CELL";
/**
 * 单元格 - 投递点
 */
export const TRANS_CELL = "TRANS_CELL";

/*************************************
 *         地图中所有线段的类型          *
 *************************************/

/**
 * 线 - 直线
 */
export const LINE_S_LINE = "S_LINE";

/**
 * 线 - 曲线
 */
export const LINE_BEZIER = "BEZIER";

/**
 * 线 - 顺时针弧线
 */
export const LINE_F_ARC = "F_ARC";

/**
 * 线 - 逆时针弧线
 */
export const LINE_B_ARC = "B_ARC";

/*************************************
 *         地图中所有设备的类型          *
 *************************************/
/**
 * 设备 - 工作站
 */
export const DEVICE_STATION = "STATION";
/**
 * 设备 - 充电站
 */
export const DEVICE_CHARGER = "CHARGER";
/**
 * 设备 - 反光柱
 */
export const DEVICE_REFLECTIVE = "MARKER";
/**
 * 设备 - 电梯
 */
export const DEVICE_ELEVATOR = "ELEVATOR";
/**
 * 设备 - 安全设备
 */
export const DEVICE_SAFE = "SAFE";

/**
 * 设备 - 输送线
 */
export const DEVICE_CONVEYOR = "CONVEYOR";
/**
 * 设备 - 托盘货叉
 */
export const DEVICE_FORK = "FORK";
/**
 * 设备 - 提升机
 */
export const DEVICE_LIFT = "LIFT";
/**
 * 设备 - 叠盘机
 */
export const DEVICE_STACKER = "STACKER";
/**
 * 设备 - ？？？
 */
export const DEVICE_MOVE_FORK = "MOVE_FORK";

/*************************************
 *         地图中所有区域的类型          *
 *************************************/

/**
 * 区域 - 十字路口(红绿灯)
 */
export const AREA_TRAFFIC_LIGHT = "TRAFFIC_LIGHT";

/**
 * 区域 - 急停区域
 */
export const AREA_STOP = "STOP";

/**
 * 区域 - 机器人调度
 */
export const AREA_ROBOT = "ROBOT";

/**
 * 区域 - 货架区域
 */
export const AREA_SHELF = "SHELF";
/**
 * 区域 - 任务控制区域
 */
export const AREA_TASK_CONTROL = "TASK_CONTROL";
/**
 * 区域 - 禁止停留区域
 */
export const AREA_NO_STAY = "NO_STAY";
/**
 * 区域 - 地图区块
 */
export const AREA_BLOCK = "BLOCK";
/**
 * 区域 - 单行道独木桥
 */
export const AREA_SINGLE_LANE = "SINGLE_LANE";
/**
 * 区域 - 交通管制区域
 */
export const AREA_TRAFFIC_CONTROL = "TRAFFIC_CONTROL";
/**
 * 区域 - 分拣区
 */
export const AREA_SORTING_AREA = "SORTING_AREA";
/**
 * 区域 - 限制弧线转弯
 */
export const AREA_RESTRICT_BIG_ARC_AREA = "RESTRICT_BIG_ARC_AREA";
export const AREA_ADD_FN = "AREA_ADD_FN";
/**
 * 区域 - 限制弧线转弯
 */
export const AREA_HIGHWAY_AREA = "HIGHWAY_AREA";
/**
 * 区域 - 清洗区域
 */
export const AREA_EMPTYING_AREA = "FORBIDDEN_AREA";
/**
 * 区域 - 清除区域
 */
export const CLEAR_ROBOT_AREA = "FORBIDDEN_AREA";
/**
 * 区域 - 其他
 */
export const AREA_CUSTOM_AREA = "CUSTOM_AREA";
//集合区域
export const GATHERING_AREA = "GATHERING_AREA";
//静态限速区域
export const STATIC_SPEED_LIMIT_AREA = "STATIC_SPEED_LIMIT_AREA";
//动态限速区域
export const REAL_TIME_SPEED_LIMIT_AREA = "REAL_TIME_SPEED_LIMIT_AREA";
//避障区域
export const OBSTACLE_AVOIDANCE_AREA = "OBSTACLE_AVOIDANCE_AREA";
//安全PLC避障区域
export const PLC_LIMIT_AREA = "PLC_LIMIT_AREA";
//高空避障区域
export const HIGH_ALTITUDE_OBSTACLE_AREA = "HIGH_ALTITUDE_OBSTACLE_AREA";
//slam导航区域
export const SLAM_NAVIGATION_AREA = "SLAM_NAVIGATION_AREA";
//关闭避障区域
export const CLOSE_OBSTACLE_AVOIDANCE_AREA = "CLOSE_OBSTACLE_AVOIDANCE_AREA";
//资源隔离区域
export const RESOURCE_ISOLATION_AREA = "RESOURCE_ISOLATION_AREA";
/*************************************
 *              常用字典表             *
 *************************************/
export const CELL_TYPE_DICT = [
  {
    value: BLOCKED_CELL,
    label: "lang.rms.web.map.cell.type.blocked",
  },
  {
    value: CHARGER_CELL,
    label: "lang.rms.web.map.cell.type.charger",
  },
  {
    value: DROP_CELL,
    label: "lang.rms.web.map.cell.type.drop",
  },
  {
    value: ELEVATOR_CELL,
    label: "lang.rms.web.map.cell.type.elevator",
  },
  {
    value: OMNI_DIR_CELL,
    label: "lang.rms.web.map.cell.type.omni.dir",
  },
  {
    value: PALLET_RACK_CELL,
    label: "lang.rms.web.map.cell.type.palletRack",
  },
  {
    value: SHELF_CELL,
    label: "lang.rms.web.map.cell.type.shelf",
  },
  {
    value: STATION_CELL,
    label: "lang.rms.web.map.cell.type.station",
  },
  {
    value: TRANS_CELL,
    label: "lang.rms.web.map.cell.type.trans",
  },
];

/*************************************
 *        完整单元格类型字典表         *
 *************************************/
const CELL_TYPE_NULL_CELL = "NULL_CELL"; // 0
const CELL_TYPE_SHELF_CELL = "SHELF_CELL"; // 1放置货架的单元格
const CELL_TYPE_E2W_PATH_CELL = "E2W_PATH_CELL"; // 2东向西过道的单元格
const CELL_TYPE_W2E_PATH_CELL = "W2E_PATH_CELL"; // 3西向东过道的单元格
const CELL_TYPE_S2N_PATH_CELL = "S2N_PATH_CELL"; // 4南向北过道的单元格
const CELL_TYPE_N2S_PATH_CELL = "N2S_PATH_CELL"; // 5北向南过道的单元格
const CELL_TYPE_E2W_S2N_PATH_CELL = "E2W_S2N_PATH_CELL"; // 6东向西过道与南向北过道交叉单元格
const CELL_TYPE_E2W_N2S_PATH_CELL = "E2W_N2S_PATH_CELL"; // 7东向西过道与北向南过道交叉单元格
const CELL_TYPE_W2E_S2N_PATH_CELL = "W2E_S2N_PATH_CELL"; // 8西向东过道与南向北过道交叉单元格
const CELL_TYPE_W2E_N2S_PATH_CELL = "W2E_N2S_PATH_CELL"; // 9西向东过道与北向南过道交叉单元格
const CELL_TYPE_E2W_W2E_PATH_CELL = "E2W_W2E_PATH_CELL"; // 10可向东西两个方向走的单元格
const CELL_TYPE_N2S_S2N_PATH_CELL = "N2S_S2N_PATH_CELL"; // 11可向南北两个方向走的单元格
const CELL_TYPE_E2W_W2E_N2S_PATH_CELL = "E2W_W2E_N2S_PATH_CELL"; // 12可向东西南三个方向走的单元格
const CELL_TYPE_E2W_W2E_S2N_PATH_CELL = "E2W_W2E_S2N_PATH_CELL"; // 13可向东西北三个方向走的单元格
const CELL_TYPE_N2S_S2N_E2W_PATH_CELL = "N2S_S2N_E2W_PATH_CELL"; // 14可向南北西三个方向走的单元格
const CELL_TYPE_N2S_S2N_W2E_PATH_CELL = "N2S_S2N_W2E_PATH_CELL"; // 15可向南北东三个方向走的单元格
const CELL_TYPE_OMNI_DIR_CELL = "OMNI_DIR_CELL"; // 16可以向四个方向行走的单元格
const CELL_TYPE_STATION_CELL = "STATION_CELL"; // 17拣货站拣货的位置
const CELL_TYPE_TURN_CELL = "TURN_CELL"; // 18拣货站转向的位置，只有标准工位田字格使用
const CELL_TYPE_QUEUE_CELL = "QUEUE_CELL"; // 19拣货站排队路径
const CELL_TYPE_CHARGER_CELL = "CHARGER_CELL"; // 20充电桩位置
const CELL_TYPE_CHARGER_PI_CELL = "CHARGER_PI_CELL"; // 21充电桩电源接口位置 Power Interface
const CELL_TYPE_BLOCKED_CELL = "BLOCKED_CELL"; // 22阻塞的单元格
const CELL_TYPE_ENTRY_CELL = "ENTRY_CELL"; // 23入口
const CELL_TYPE_EXIT_CELL = "EXIT_CELL"; // 24出口
const CELL_TYPE_ELEVATOR_CELL = "ELEVATOR_CELL"; // 25电梯
const CELL_TYPE_DROP_CELL = "DROP_CELL"; // 投递点
const CELL_TYPE_PALLET_RACK_CELL = "PALLET_RACK_CELL"; // 托盘架
const CELL_TYPE_BOX_RACK_CELL = "BOX_RACK_CELL"; // 固定货架

export const CELL_TYPE_ALL_DICT = [
  {
    value: CELL_TYPE_NULL_CELL,
    label: "lang.rms.web.map.cell.type.emptyNode",
  },
  {
    value: CELL_TYPE_SHELF_CELL,
    label: "lang.rms.web.map.cell.type.shelf",
  },
  {
    value: CELL_TYPE_E2W_PATH_CELL,
    label: "lang.rms.web.map.cell.type.oneWayAisleByew",
  },
  {
    value: CELL_TYPE_W2E_PATH_CELL,
    label: "lang.rms.web.map.cell.type.oneWayAisleBywe",
  },
  {
    value: CELL_TYPE_S2N_PATH_CELL,
    label: "lang.rms.web.map.cell.type.oneWayAisleBysn",
  },
  {
    value: CELL_TYPE_N2S_PATH_CELL,
    label: "lang.rms.web.map.cell.type.oneWayAisleByns",
  },
  {
    value: CELL_TYPE_E2W_S2N_PATH_CELL,
    label: "lang.rms.web.map.cell.type.crossoverByEwSN",
  },
  {
    value: CELL_TYPE_E2W_N2S_PATH_CELL,
    label: "lang.rms.web.map.cell.type.crossoverByEwNs",
  },
  {
    value: CELL_TYPE_W2E_S2N_PATH_CELL,
    label: "lang.rms.web.map.cell.type.crossoverByWeSn",
  },
  {
    value: CELL_TYPE_W2E_N2S_PATH_CELL,
    label: "lang.rms.web.map.cell.type.crossoverByEwNs",
  },
  {
    value: CELL_TYPE_E2W_W2E_PATH_CELL,
    label: "lang.rms.web.map.cell.type.twoWayAisleByew",
  },
  {
    value: CELL_TYPE_N2S_S2N_PATH_CELL,
    label: "lang.rms.web.map.cell.type.twoWayAisleByns",
  },
  {
    value: CELL_TYPE_E2W_W2E_N2S_PATH_CELL,
    label: "lang.rms.web.map.cell.type.threeWayCorridorByews",
  },
  {
    value: CELL_TYPE_E2W_W2E_S2N_PATH_CELL,
    label: "lang.rms.web.map.cell.type.threeWayCorridorByewn",
  },
  {
    value: CELL_TYPE_N2S_S2N_E2W_PATH_CELL,
    label: "lang.rms.web.map.cell.type.threeWayCorridorBynsw",
  },
  {
    value: CELL_TYPE_N2S_S2N_W2E_PATH_CELL,
    label: "lang.rms.web.map.cell.type.threeWayAisleBynse",
  },
  {
    value: CELL_TYPE_OMNI_DIR_CELL,
    label: "lang.rms.web.map.cell.type.omni.dir",
  },
  {
    value: CELL_TYPE_STATION_CELL,
    label: "lang.rms.fed.workstation",
  },
  {
    value: CELL_TYPE_TURN_CELL,
    label: "工作站转向",
  },
  {
    value: CELL_TYPE_QUEUE_CELL,
    label: "lang.rms.web.map.cell.type.QRCell",
  },
  {
    value: CELL_TYPE_CHARGER_CELL,
    label: "lang.rms.web.map.cell.type.chargingStation",
  },
  {
    value: CELL_TYPE_CHARGER_PI_CELL,
    label: "lang.rms.web.map.cell.type.chargingStationPowerSupply",
  },
  {
    value: CELL_TYPE_BLOCKED_CELL,
    label: "lang.rms.web.map.cell.type.blocked",
  },
  {
    value: CELL_TYPE_ENTRY_CELL,
    label: "lang.rms.web.map.cell.type.inlet",
  },
  {
    value: CELL_TYPE_EXIT_CELL,
    label: "lang.rms.web.map.cell.type.export",
  },
  {
    value: CELL_TYPE_ELEVATOR_CELL,
    label: "lang.rms.fed.elevator",
  },
  {
    value: CELL_TYPE_DROP_CELL,
    label: "lang.rms.web.map.cell.type.drop",
  },
  {
    value: CELL_TYPE_PALLET_RACK_CELL,
    label: "lang.rms.web.map.cell.type.palletRack",
  },
  {
    value: CELL_TYPE_BOX_RACK_CELL,
    label: "lang.rms.web.map.cell.type.fixedShelfPoint",
  },
];
