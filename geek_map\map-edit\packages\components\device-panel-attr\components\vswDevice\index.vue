<template>
  <el-tabs v-model="activeNames" class="vsw-tabs">
    <el-tab-pane
      v-if="pppData"
      :label="t('lang.rms.map.deviceEdit.pppStationConfig')"
      name="pppStation"
    >
      <PPPStationPanel
        v-if="pppData"
        ref="nodeRef"
        :title="pppTitle"
        :curData="pppData"
        :viewDevice="viewDevice"
        @trigger="triggerHandler"
      />
    </el-tab-pane>
    <el-tab-pane
      v-if="vswData"
      :label="t('lang.rms.map.deviceEdit.vswDeviceConfig')"
      name="vswDevice"
    >
      <vswDevice
        v-if="vswData"
        :vswData="vswData"
        :viewDevice="viewDevice"
        @trigger="triggerHandler"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { computed, ComputedRef, ref, watch, reactive } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useI18n } from "@packages/hook/useI18n";

import PPPStationPanel from "../pppStation/index.vue";
import vswDevice from "./components/vswDevice.vue";

import { triggerEventListener } from "@packages/hook/useEvent";
import { useEditMap } from "@packages/hook/useEdit";

const { t } = useI18n();

const pppTitle = ref('')

const props = defineProps<{
  title: string;
  pppData: object;
  vswData: object;
  viewDevice: boolean;
}>();

let activeNames = ref(props.pppData ? 'pppStation' : 'vswDevice');

function triggerHandler() {
  emits("trigger", null);
}

/**
 * 可用的事件
 */
const emits = defineEmits<{
  (event: "trigger", data: any): void;
}>();

</script>

<style scoped lang="scss">
.vsw-tabs {
  ::v-deep .el-tabs__content {
    // padding: 32px;
    // color: #6b778c;
    font-size: 32px;
    font-weight: 600;
    min-width: 30vw;
  }
}
</style>
