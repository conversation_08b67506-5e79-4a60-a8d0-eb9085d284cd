/* ! <AUTHOR> at 2025/03/28 */
/**
 * @description
 * 用于查找当前文件夹下所用的ws消息指令名称
 * 结果文件路径：./build/node_bash/searchMsgTypeResults.txt
 * 使用方法：在项目根目录下运行：node .\build\node_bash\filterWsMsgName.js
 */

const fs = require("fs");
const path = require("path");

// 设置要搜索的文件夹路径和关键词
const folderPath = "./"; //文件夹路径
const foldersToSkip = ["node_modules", "dist", "build", "geek_map\\map-edit", ".git"]; // 需要跳过的文件夹名称
const outputPath = "./build/node_bash/searchMsgTypeResults.txt"; // 输出文件路径

// 用于存储搜索结果
const searchResults = new Set();

// 递归遍历文件夹
function traverseFolder(folder) {
  const files = fs.readdirSync(folder);

  files.forEach(file => {
    const filePath = path.join(folder, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      const isSkip = foldersToSkip.some(folderName => {
        if (filePath.includes(folderName)) return true;
      });
      if (isSkip) return;
      traverseFolder(filePath);
    } else {
      if (filePath.endsWith(".js") || filePath.endsWith(".vue") || filePath.endsWith(".ts")) {
        // 根据需要修改文件类型过滤器
        searchKeywordInFile(filePath);
      }
    }
  });
}

// 在文件中搜索关键词
function searchKeywordInFile(file) {
  const content = fs.readFileSync(file, "utf-8");
  const lines = content.split("\n");

  lines.forEach((line, index) => {
    // 跳过行内容包含.html的行
    if (line.includes(".html")) {
      return;
    }
    const msgType = getMsgType(line);
    if (msgType) {
      console.log(`msgType: ${msgType}`);
      console.log("----------------------");
      searchResults.add(msgType);
    }
  });
}

// 提取接口地址
function getMsgType(line) {
  if (line.includes('RequestMsg"') || line.includes('ResponseMsg"')) {
    // 提取第一个和第二个双引号之间的内容
    const start = line.indexOf('"') + 1; // 跳过开头的双引号
    const end = line.indexOf('"', start);
    const result = line.substring(start, end);

    if (result.length > 10) return result;
    else return null;
  }

  return null;
}

// 将结果写入文件
function writeResultsToFile() {
  let outputContent = "";

  searchResults.forEach(result => {
    outputContent += `指令名称: ${result}\n`;
  });

  fs.writeFileSync(outputPath, outputContent);
  console.log(`搜索结果已写入文件: ${outputPath}`);
}

traverseFolder(folderPath);
writeResultsToFile();
