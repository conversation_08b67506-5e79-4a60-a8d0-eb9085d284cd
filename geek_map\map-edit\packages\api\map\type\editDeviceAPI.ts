import type { MapPPPStationDto } from "@packages/type/editDevice";

export interface FindPppStationParams {
  mapId: string | number;
  stationId: string | number;
}

//可能没有 data 的返回
export interface FindPppStationResult {
  data: MapPPPStationDto;
}

export interface UpdatePppStationParams {
  id: string | number;
  mapId: string | number;
  deviceCode: string | number;
  xlocationOffset: string | number;
  ylocationOffset: string | number;
  left: any;
  right: any;
}

//可能没有 data 的返回
export interface UpdatePppStationResult {
  data: any;
}
