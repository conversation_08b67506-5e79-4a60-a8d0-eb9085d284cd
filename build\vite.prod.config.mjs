/* ! <AUTHOR> at 2024/10/31 */
// 生产环境配置
import { defineConfig } from "vite";
import { viteStaticCopy } from "vite-plugin-static-copy";

export default defineConfig(mode => {
  return {
    base: "./",
    build: {
      assetsInlineLimit: 0, // 0表示不内联任何资源，所有资源都将作为单独的文件处理
      cssCodeSplit: true, // 是否将 CSS 提取为独立的文件
      sourcemap: false,
      rollupOptions: {
        output: {
          manualChunks: {
            "vender.vue": ["vue", "vue-router", "vue-i18n", "pinia", "axios"],
            "vender.vue.ui": ["vuedraggable", "nprogress"],
            "vender.map2d": ["geek-map2d"],
            "vender.model3d": ["geek-model3d"],
            "vender.echarts": ["echarts"],
          },
        },
      },
      minify: "terser", // 压缩器
      terserOptions: {
        compress: {
          drop_console: true, // 删除 console
          drop_debugger: true, // 删除 debugger
          dead_code: true, // 删除无用代码
        },
        output: {
          comments: false, // 删除注释
          beautify: false, // 紧凑输出
        },
      },
    },
    plugins: [
      viteStaticCopy({
        targets: [
          { src: "./static", dest: "./" },
          {
            src: "./geek_map/map-edit/dist",
            dest: "./",
            rename: "singleEdit2D",
          },
        ],
      }),
    ],
  };
});
