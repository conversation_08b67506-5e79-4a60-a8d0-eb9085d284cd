import { usePureNumber, useCodeQualified, useRequired } from "@packages/hook/useRules";
import { NodeAttrEditConf } from "@packages/type/editUiType";
import DICT from "@packages/configure/dict";
import { useI18n } from "@packages/hook/useI18n";

export const BASE_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  const { t } = useI18n();
  return {
    name: "base",
    tabTitle: "lang.rms.fed.basis",
    labelWidth: "85px",
    labelPosition: "left",
    formItem: [
      // 二维码单元格
      {
        prop: "isQrNode",
        label: t("lang.rms.web.map.cell.type.QRCell"),
        component: "elCheckbox",
        style: {
          width: "140px",
        },
        border: true,
        onlyComponent: true,
      },
      // 节点编码
      {
        prop: "cellCode",
        label: "lang.rms.fed.cellCode",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        rules: [usePureNumber()],
      },
      // 二维码码值
      {
        prop: "qrCode",
        label: "lang.rms.fed.qrCodeValue",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        rules: [usePureNumber()],
        appendAttrsFn(value: string, data: any) {
          return { condition: data.isQrNode };
        },
      },
      // 外部编码
      {
        prop: "hostCode",
        label: "lang.rms.fed.hostCode",
        component: "elInput",
        maxlength: 32,
        showWordLimit: true,
        rules: [useRequired()],
      },
      // 节点类型
      {
        prop: "cellType",
        label: "lang.rms.fed.cellType",
        component: "elSelect",
        showWordLimit: true,
        appendAttrsFn(value: string) {
          const data = [];
          if (DICT.CELL_TYPE_DICT.find((item: { value: string }) => item.value === value)) {
            data.push(...DICT.CELL_TYPE_DICT);
          } else {
            const selTypeDict = DICT.CELL_TYPE_ALL_DICT.find((item: { value: string }) => item.value === value);
            data.push(selTypeDict, ...DICT.CELL_TYPE_DICT);
          }
          return { data };
        },
      },
      // 叉车角度
      {
        prop: "needAngles",
        label: "lang.rms.fed.needAngles",
        component: "elSelect",
        data: DICT.ANGLE_DICT,
        multiple: true,
      },
      // 容器角度 TODO: 没用
      // {
      //   prop: "containerAngle",
      //   label: "容器角度",
      //   component: "elSelect",
      //   data: DICT.ANGLE_DICT,
      // },
      // 二维码偏航角度
      {
        prop: "yawAngle",
        label: "lang.rms.fed.qrAngle",
        labelWidth: "120px",
        component: "elInputNumber",
        min: -180,
        max: 180,
        step: 90,
        appendAttrsFn(value: string, data: any) {
          return { condition: data.isQrNode };
        },
      },
      
    ],
  };
};
