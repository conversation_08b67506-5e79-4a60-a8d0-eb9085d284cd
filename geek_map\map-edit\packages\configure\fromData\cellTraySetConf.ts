import { NodeAttrEditConf } from "@packages/type/editUiType";

export const TRAY_SET_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  return {
    name: "traySet",
    tabTitle: "lang.rms.fed.palletLattice",
    labelWidth: "85px",
    labelPosition: "left",
    formItem: [
      // 托盘位的设置
      {
        prop: "palletLattices",
        appendSlotName: "cellTraySetSolt",
        onlyComponent: true,
        get(allData: { [k: string]: any }) {
          return allData.palletRackDto?.palletLattices || [];
        },
        set(value: any[], allData: { [k: string]: any }) {
          allData.palletRackDto || (allData.palletRackDto = { angle: 0 });
          allData.palletRackDto.palletLattices = value;
        }
      },
    ],
  };
};