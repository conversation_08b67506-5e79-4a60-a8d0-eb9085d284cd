<template>
  <div>
    <!-- 多层需要显示托盘位选择 -->
    <pallet-rack-lattice-select
      v-if="radioValue !== 'single'"
      v-model:latticeLayer="selectLatticeLayer"
      v-model:ruleValue="selectRule"
    />

    <div v-show="!operation" class="component-btn-group">
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.palletRackLock')"
        size="small"
        type="primary"
        @click="controlHandler('lockPalletRack')"
      >
        {{ $t("lang.rms.fed.lockPalletRack") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.palletRackLock')"
        size="small"
        type="primary"
        class="dark"
        @click="controlHandler('unlockPalletRack')"
      >
        {{ $t("lang.rms.fed.unlockPalletRack") }}
      </el-button>

      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.palletLatticeLock')"
        v-show="btnShow.lockLattice"
        size="small"
        type="primary"
        @click="controlHandler('lockLattice')"
      >
        {{ $t("lang.rms.fed.lockPalletLattice") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.palletLatticeLock')"
        v-show="btnShow.unlockLattice"
        size="small"
        type="primary"
        class="dark"
        @click="controlHandler('unlockLattice')"
      >
        {{ $t("lang.rms.fed.unlockPalletLattice") }}
      </el-button>

      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.palletLock')"
        v-show="btnShow.lockPallet"
        size="small"
        type="primary"
        @click="controlHandler('lockPallet')"
      >
        {{ $t("lang.rms.fed.lockPallet") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.palletLock')"
        v-show="btnShow.unlockPallet"
        size="small"
        type="primary"
        class="dark"
        @click="controlHandler('unlockPallet')"
      >
        {{ $t("lang.rms.fed.unlockPallet") }}
      </el-button>

      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.palletUpdate')"
        v-show="btnShow.updatePallet"
        size="small"
        type="primary"
        @click="controlHandler('updatePallet')"
      >
        {{ $t("lang.rms.fed.update") }}
      </el-button>
      <el-button
        v-if="getBtnPermission('auth.rms.monitor.button.palletMove')"
        v-show="btnShow.movePallet"
        size="small"
        type="primary"
        @click="controlHandler('movePallet')"
      >
        {{ $t("lang.rms.fed.move") }}
      </el-button>
    </div>

    <drawer-panel
      ref="drawer"
      title="lang.rms.fed.palletRack"
      :code="palletRackData?.shelfCode || ''"
    >
      <current-pallet-rack :palletRackData="palletRackData" />

      <target-pallet-rack
        v-if="targetRackCode"
        :rackCode="targetRackCode"
        :latticeCode="targetLatticeCode"
      />
    </drawer-panel>

    <pallet-rack-update
      v-if="operation === 'updatePallet'"
      :targetRackCode="targetRackCode"
      @updateTargetRackCode="val => (targetRackCode = val)"
      @updateTargetLatticeCode="val => (targetLatticeCode = val)"
      @cancel="operation = undefined"
    />

    <pallet-rack-move
      v-if="operation === 'movePallet'"
      :targetRackCode="targetRackCode"
      @updateTargetRackCode="val => (targetRackCode = val)"
      @updateTargetLatticeCode="val => (targetLatticeCode = val)"
      @cancel="operation = undefined"
    />

    <detail
      v-if="palletRackData"
      :data="palletRackData"
      :loadingViewExImg="loadingViewExImg"
      @errorHandle="data => setViewExceptionImg(data)"
    />
  </div>
</template>

<script>
import isEqual from "lodash/isEqual";
import { mapState, mapActions } from "pinia";
import { useMap2dStore } from "@map2d/dataStore/index";
import { useMap2dContainerStore } from "@map2d/dataStore/right-panel/container";
import { useRootMenuStore } from "@stores/rootMenuStore";
import { getMap2D } from "@map2d/singleton";
import DrawerPanel from "@map2d/packages/common/drawer-panel.vue";
import CurrentPalletRack from "./pallet-rack-current.vue";
import TargetPalletRack from "./pallet-rack-target.vue";
import PalletRackUpdate from "./pallet-rack-update.vue";
import PalletRackMove from "./pallet-rack-move.vue";
import Detail from "./pallet-rack-detail.vue";
import PalletRackLatticeSelect from "./pallet-rack-lattice-select.vue";

export default {
  name: "map-container-pallet-rack",
  components: {
    DrawerPanel,
    CurrentPalletRack,
    TargetPalletRack,
    PalletRackUpdate,
    PalletRackMove,
    Detail,
    PalletRackLatticeSelect,
  },
  data() {
    return {
      palletRackData: null,
      targetRackCode: "",
      targetLatticeCode: "",
      operation: undefined, // updatePallet | movePallet
      mapClickFlag: false,
      selectLatticeLayer: [],
      selectRule: "0",
    };
  },
  computed: {
    ...mapState(useMap2dStore, [
      "mapClickData",
      "wsQueryTimer",
      "loadingViewExImg",
      "isRightDrawerVisible",
    ]),
    ...mapState(useMap2dContainerStore, [
      "radioValue",
      "containerCode",
      "searchData",
      "multiRackCodes",
      "rackCurrentSelected",
    ]),
    ...mapState(useRootMenuStore, ["getBtnPermission"]),

    btnShow() {
      const selected = this.rackCurrentSelected;
      let data = {
        lockLattice: false,
        unlockLattice: false,
        lockPallet: false,
        unlockPallet: false,
        updatePallet: false,
        movePallet: false,
      };

      const selectedType = selected?.type || "";
      switch (selectedType) {
        case "lattice":
          if (selected.hasOwnProperty("___latticeLock")) {
            data.lockLattice = !selected.___latticeLock;
            data.unlockLattice = !data.lockLattice;
          } else {
            const lattices = selected.lattices || [];
            const isUnlockedIndex = lattices.findIndex(item => item?.latticeFlag != "LOCKED");
            const isLockedIndex = lattices.findIndex(item => item?.latticeFlag == "LOCKED");
            data.lockLattice = isUnlockedIndex != -1;
            data.unlockLattice = isLockedIndex != -1;
          }
          data.lockPallet = false;
          data.unlockPallet = false;
          data.updatePallet = false;
          data.movePallet = false;
          break;
        case "pallet":
          const lattice = selected.lattice;
          if (selected.hasOwnProperty("___latticeLock")) {
            data.lockLattice = !selected.___latticeLock;
          } else {
            data.lockLattice = lattice?.latticeFlag != "LOCKED";
          }
          data.unlockLattice = !data.lockLattice;

          if (selected.hasOwnProperty("___palletLock")) data.lockPallet = !selected.___palletLock;
          else data.lockPallet = lattice?.occupiedContainer?.lockedState != "LOCKED";
          data.unlockPallet = !data.lockPallet;
          data.updatePallet = true;
          data.movePallet = true;
          break;
        default:
          break;
      }

      return data;
    },
  },
  watch: {
    radioValue: {
      handler(val, oldVal) {
        if (!oldVal) return;
        this.clearData();
        const { mapRender } = getMap2D() || {};
        if ((val === "multi" && oldVal === "single") || val !== "multi") {
          this.setContainerCode("");
          this.setMultiRackCodes([]);
          mapRender && mapRender.clearSelects();
        }

        switch (val) {
          case "multi":
          case "rect":
            this.setRightOccupyState(true);
            this.setMapClickStatus({ layer: ["pallet"], isMulti: true });
            if (val == "rect") this.rectSelect();
            else mapRender && mapRender.off("rect");
            break;
          default:
            this.setRightOccupyState(false);
            mapRender && mapRender.off("rect");
            this.setMapClickStatus({ layer: [], isMulti: false });
            break;
        }
      },
      immediate: true,
    },

    mapClickData: {
      handler(data, oldData) {
        if (!oldData && !data) return;
        const layer = data?.layer || "";
        if (!["pallet"].includes(layer)) {
          this.clearData();
          this.setContainerCode("");
          return;
        }

        const radioValue = this.radioValue;
        switch (radioValue) {
          case "single":
            this.mapClickFlag = true;
            const code = data.code.toString();
            if (this.operation) {
              if (layer == "pallet") this.targetRackCode = code;
            } else if (this.containerCode !== code && layer == "pallet") {
              this.setContainerCode(code);
            }
            break;
          case "multi":
            if (layer !== "pallet") return;
            const { mapRender } = getMap2D() || {};
            const codes = mapRender.getSelectedCodes();
            this.setMultiRackCodes(codes?.pallet || []);
            break;
        }
      },
      immediate: true,
    },

    containerCode: {
      handler(code) {
        this.clearData();
        if (code) {
          this.mapClickFlag = true;
          this.queryPalletRackData();
        }
      },
      immediate: true,
    },

    operation(val, oldVal) {
      this.targetRackCode = "";
      this.targetLatticeCode = "";
      this.setRackTargetSelected(null);
      this.setRightOccupyState(!!val);

      switch (oldVal) {
        case "updatePallet":
        case "movePallet":
          this.setRackCurrentSelected(null);
          const code = this.containerCode;
          const { mapRender } = getMap2D();
          if (code) mapRender.select({ pallet: [code] });
          else mapRender.clearSelects();
          break;
      }
    },
  },

  beforeUnmount() {
    this.setRightOccupyState(false);
    this.clearData();
    Object.assign(this.$data, this.$options.data());
  },

  methods: {
    ...mapActions(useMap2dStore, [
      "setRightOccupyState",
      "setWsQueryTimer",
      "setViewExceptionImg",
      "setMapClickStatus",
    ]),
    ...mapActions(useMap2dContainerStore, [
      "setContainerCode",
      "setSearchData",
      "setMultiRackCodes",
      "setRackCurrentSelected",
      "setRackTargetSelected",
    ]),

    closeDrawer() {
      if (!this.isRightDrawerVisible) return;
      const $drawer = this.$refs["drawer"];
      $drawer && $drawer.close();
    },

    clearData() {
      this.setWsQueryTimer(null);
      this.palletRackData = null;
      this.targetRackCode = "";
      this.targetLatticeCode = "";
      this.setRackCurrentSelected(null);
      this.setRackTargetSelected(null);
    },

    queryPalletRackData() {
      this.setWsQueryTimer(null);

      const code = this.containerCode;
      const { mapWorker } = getMap2D();
      mapWorker.wsDataQuery("PALLET_RACK", code).then(res => {
        if (res?.header?.code !== 0) {
          this.$error(this.$t(res?.header?.msg));
          return;
        }

        const data = res?.body;
        if (!data) {
          this.clearData();
          this.closeDrawer();
          this.setSearchData(null);
          this.setContainerCode("");
          return;
        }
        data.shelfCode = code;
        if (this.mapClickFlag) {
          const $drawer = this.$refs["drawer"];
          if ($drawer) {
            $drawer.open();
            this.mapClickFlag = false;
          }
        }
        if (!isEqual(data, this.palletRackData)) this.palletRackData = data;

        const _timer = setTimeout(() => {
          this.queryPalletRackData(code);
        }, 300);
        this.setWsQueryTimer(_timer);
      });
    },

    rectSelect() {
      const { mapRender } = getMap2D() || {};
      mapRender
        .rect()
        .then(rectData => {
          $req
            .post("/athena/warehouse/cell/queryByLocationRange", {
              startLocation: rectData["points"][0],
              stopLocation: rectData["points"][1],
              type: 4, // 托盘支架类型
            })
            .then(res => {
              const data = res.data || [];
              const codes = data.map(item => item.rackCode);
              mapRender.select({ pallet: codes });
              this.setMultiRackCodes(codes);
              this.$emit("rectEnd");
            });
        })
        .catch(e => {
          this.$emit("rectEnd");
          if ([-1, -2].includes(e.code)) {
            this.$error(this.$t("lang.rms.api.result.task.startOrDestOutOfMap"));
          }
          const { error } = console;
          error(e);
        });
    },

    controlHandler(cmd) {
      switch (cmd) {
        case "lockPalletRack":
          this._operatePalletRack("LOCK_PALLET_RACK");
          return;
        case "unlockPalletRack":
          this._operatePalletRack("UNLOCK_PALLET_RACK");
          return;
        case "lockLattice":
          this._operateLattice("LOCK_PALLET_LATTICE");
          return;
        case "unlockLattice":
          this._operateLattice("UNLOCK_PALLET_LATTICE");
          return;
        case "lockPallet":
          this._operatePallet("LOCK_PALLET");
          return;
        case "unlockPallet":
          this._operatePallet("UNLOCK_PALLET");
          return;
        case "updatePallet":
        case "movePallet":
          this.operation = cmd;
          return;
      }
    },

    _operatePallet(cmd) {
      this.operation = undefined;
      const selected = this.rackCurrentSelected;
      if (selected?.type !== "pallet") return;

      const map2D = getMap2D();
      map2D.mapWorker
        .wsRequest("PalletInstructionRequestMsg", {
          instruction: cmd,
          palletCode: selected.lattice.occupiedContainer?.shelfCode,
        })
        .then(res => {
          const msg = $utils.Tools.transMsgLang(res?.header?.msg || "");
          if (res?.header?.code === 0) {
            this.$success(msg);
            if (cmd == "LOCK_PALLET") selected.___palletLock = true;
            else selected.___palletLock = false;
          } else this.$error(msg);
        });
    },

    _operateLattice(cmd) {
      this.operation = undefined;
      const selected = this.rackCurrentSelected;
      const selectedType = selected?.type || "";
      if (!["lattice", "pallet"].includes(selectedType)) return;
      let latticeCodeList = [];
      if (selectedType == "lattice") {
        latticeCodeList = selected.lattices.map(item => item.palletLatticeCode);
      } else {
        latticeCodeList = [selected.lattice.palletLatticeCode];
      }

      const map2D = getMap2D();
      map2D.mapWorker
        .wsRequest("PalletInstructionRequestMsg", {
          instruction: cmd,
          latticeCodeList,
        })
        .then(res => {
          const msg = $utils.Tools.transMsgLang(res?.header?.msg || "");
          if (res?.header?.code === 0) {
            if (cmd == "LOCK_PALLET_LATTICE") selected.___latticeLock = true;
            else selected.___latticeLock = false;
            this.$success(msg);
          } else this.$error(msg);
        });
    },

    _operatePalletRack(cmd) {
      this.operation = undefined;
      let rackCodes = [];
      if (this.radioValue === "single") rackCodes = [this.containerCode];
      else rackCodes = this.multiRackCodes;

      const map2D = getMap2D();
      map2D.mapWorker
        .wsRequest("PalletInstructionRequestMsg", {
          instruction: cmd,
          rackCode: rackCodes[0], // 单个托盘支架
        })
        .then(res => {
          const msg = $utils.Tools.transMsgLang(res?.header?.msg || "");
          if (res?.header?.code === 0) this.$success(msg);
          else this.$error(msg);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.component-btn-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.dark {
  background-color: #606266;
  border-color: #606266;
}
</style>
