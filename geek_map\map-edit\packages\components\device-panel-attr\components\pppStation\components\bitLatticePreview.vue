<template>
  <el-form :inline="true" label-width="90px" :model="formData">
    <el-form-item :label="$t('lang.rms.fed.numOfCacheBitLayers')">
      <el-input-number
        class="w100 col-input"
        v-model="formData.rowNum"
        :min="1"
        :max="20"
        controls-position="right"
      />
    </el-form-item>

    <el-form-item :label="$t('lang.rms.fed.numberColumns')">
      <el-input-number
        class="w100 col-input"
        v-model="formData.colNum"
        :min="1"
        :max="20"
        controls-position="right"
      />
    </el-form-item>
  </el-form>
  <div class="cacheBitPreview" v-if="cacheBitData.length && cacheBitData[0].cols.length">
    <div class="reverse-list">
      <div class="row" v-for="(rowItem, index) in cacheBitData" :key="index">
        <span class="row-title" :title="$t('lang.rms.fed.columnTo', [rowItem.line])">
          {{ $t("lang.rms.fed.layerTo", [rowItem.line]) }}
        </span>
        <div class="col" v-for="(colItem, colIndex) in rowItem.cols || []" :key="`col_${colIndex}`">
          <div class="item">
            <el-input-number class="w50 col-input" v-model="colItem.locationX" :controls="false" />
            ,
            <el-input-number class="w50 col-input" v-model="colItem.locationY" :controls="false" />
          </div>
        </div>
      </div>
    </div>
    <div class="row last-row">
      <span class="row-title"></span>
      <div class="col" v-for="(colItem, index) in cacheBitData[0].cols || []">
        <div class="item">
          {{ $t("lang.rms.fed.columnTo", [index + 1]) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, Ref, defineProps, nextTick } from "vue";

const props = defineProps<{
  options: any;
  defRowNum: number;
  defColNum: number;
}>();

const formData = ref({
  transferDirection: "",
  rowNum: props.defRowNum || 4,
  colNum: props.defColNum || 3,
});

const cacheBitData: Ref<any> = ref(getBitDataList());

function getBitDataList() {
  const { rowNum, colNum } = formData.value;

  const list = new Array(rowNum).fill([]).map((_, index) => {
    return {
      line: index + 1,
      cols: new Array(colNum).fill({}).map(item => {
        return { locationX: 0, locationY: 0 };
      }),
    };
  });
  return list;
}
// 当rowNum变化时, 要对cacheBitData数值进行切割
watch(
  () => formData.value.rowNum,
  (newVal, oldVal) => {
    if (!newVal) {
      cacheBitData.value = [];
      return;
    }

    if (newVal > oldVal) {
      const addList = new Array(newVal - oldVal).fill([]).map((_, index) => {
        return {
          line: oldVal + index + 1,
          cols: new Array(formData.value.colNum).fill({}).map(item => {
            return { locationX: 0, locationY: 0 };
          }),
        };
      });
      cacheBitData.value.push(...addList);
    } else {
      cacheBitData.value.splice(newVal || 0);
    }
  },
);

// 当colNum变化时, 要对cacheBitData数值进行切割
watch(
  () => formData.value.colNum,
  newVal => {
    cacheBitData.value.forEach((row: any) => {
      const len = row.cols.length;
      if (newVal > len) {
        row.cols.push(
          ...new Array(newVal - len).fill({}).map(item => {
            return { locationX: 0, locationY: 0 };
          }),
        );
      } else {
        row.cols.splice(newVal || 0);
      }
    });
    console.log(cacheBitData.value);
  },
);

watch(
  () => props.options,
  option => {
    if (option.length === 0) {
      return;
    }

    // 得到最大行和最大列, 数据回填
    let maxLine = 1;
    let maxLayer = 1;
    option.forEach((item: any) => {
      const { line, layerColumnNum } = item;
      if (maxLine < line) {
        maxLine = line;
      }

      if (maxLayer < layerColumnNum) {
        maxLayer = layerColumnNum;
      }

      nextTick(() => {
        option.forEach((item: any) => {
          const { id, locationX, locationY, line, layerColumnNum } = item;
          const appData = cacheBitData.value[line - 1].cols[layerColumnNum - 1];
          appData.locationX = locationX;
          appData.locationY = locationY;
          appData.id = id;
        });
      });
    });
    formData.value.rowNum = maxLine;
    formData.value.colNum = maxLayer;
  },
  {
    immediate: true,
  },
);
function getlatticeCodeNumber(number: number): string {
  if (number >= 10) {
    return `${number}`;
  }
  return `0${number}`;
}

function getCacheBitData({ stopId, position }: any) {
  const transferLatticeInfos: any[] = [];
  cacheBitData.value.forEach((item: any) => {
    const { line, cols } = item;
    cols.forEach((colItem: any, index: number) => {
      const { locationX, locationY } = colItem;
      const layerColumnNum = index + 1;
      const lineCodeStr = getlatticeCodeNumber(line);
      const layerCodeStr = getlatticeCodeNumber(layerColumnNum);
      transferLatticeInfos.push({
        id: colItem.id,
        line,
        locationX,
        locationY,
        layerColumnNum,
        latticeCode: `${position}${stopId}${lineCodeStr}${layerCodeStr}`,
      });
    });
  });
  return transferLatticeInfos;
}

defineExpose({
  getCacheBitData,
});
</script>

<style lang="less" scoped>
.w100 {
  width: 120px;
}
.w50 {
  width: 50px;
}

.cacheBitPreview {
  overflow: auto;
  width: 100%;
  max-height: 210px;

  .row {
    white-space: nowrap;

    .row-title {
      display: inline-block;
      width: 60px;
      text-align: right;
      margin-right: 5px;
      // css 不换行, 超出省略
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 34px;
      vertical-align: bottom;
    }

    .col {
      display: inline-block;
      width: 131px;
      padding: 5px;
      text-align: center;
      // 禁止换行
      white-space: nowrap;
      border: 1px solid #999;
      border-right: none;
      border-bottom: none;

      // 最后一列, border-right
      &:last-child {
        border-right: 1px solid #999;
      }
    }
  }

  .reverse-list {
    display: flex;
    flex-direction: column-reverse;
  }

  // 最后一行
  .last-row {
    .col {
      width: 132px;
      border-left: none !important;
      border-right: none !important;
    }
  }

  .col-input {
    :deep(.el-input__wrapper) {
      padding-right: 1px;
      padding-left: 1px;
    }
  }
}
</style>
