<template>
  <div class="traySets">
    <div class="traySetItem" v-for="(item, index) in curPalletLatticeList" :key="index">
      <FromBase
        :formItem="curPalletLatticeFormItem"
        :formData.async="item"
        size="mini"
        label-width="110px"
        @update:form-data="(changeItem) => changeFormBase(index, changeItem)">
        <template #heightSlot="{ option }">
          {{ option.fromData.height }}mm
        </template>
      </FromBase>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs, ref, watch } from "vue";
import { useAttrStore } from "@packages/store/attr"
import FromBase from "@packages/components/map-panel-attr/components/base/fromBase.vue"

const attrStore = useAttrStore();

interface FormDataType {
  [k: string]: any;
}

const props = defineProps<{
  fromData: FormDataType;
  updateValue: Function;
}>();

const { fromData } = toRefs(props);

// 当前数据
const curPalletLatticeList = ref(fromData.value.palletRackDto.palletLattices || [])

const curPalletLatticeFormItem = [
  {
    prop: "palletLatticeCode",
    label: "lang.rms.fed.palletLatticeCode",
    component: "elInput",
    maxlength: 16,
    showWordLimit: true,
  },
  {
    prop: "palletLatticeHostCode",
    label: "lang.rms.fed.hostCode",
    component: "elInput",
    maxlength: 16,
    showWordLimit: true,
  },
  {
    prop: "layer",
    label: "lang.rms.palletPositionManage.layer",
  },
  {
    prop: "height",
    label: "lang.fed.rms.heightFromGround",
    appendSlotName: "heightSlot"
  },
]

// 对应的模型ID
const palletRackModelId = computed(() => {
  return fromData.value.palletRackDto.palletRackModelId;
});

// 对应的模型数据
const curContainerModelDict = computed(() => {
  const palletRackModeId = palletRackModelId.value;
  const containerModelDict = attrStore.containerModelDict || [];
  return containerModelDict.find(item => {
    return item.value === palletRackModeId
  })
});

// 当前数据发生变更
watch(palletRackModelId, () => {
  updateCurPalletLatticeList()
  updateFormData()
}, {
  immediate: true
});

watch(() => fromData.value.palletRackDto.palletLattices, () => {
  updateCurPalletLatticeList()
}, {
  immediate: true
});


// 更新当前托盘位及数据
function updateCurPalletLatticeList() {
  const list = fromData.value.palletRackDto.palletLattices || []
  const detail = curContainerModelDict.value?.detail || []
  const len = detail.length

  if (list.length > len) {
    list.splice(len)
  }
  
  let height = 0;
  curPalletLatticeList.value = [...new Array(len)].map((item, index) => {
    const listItem = list[index];
    const detailItem: any = detail[index];
    height += (detailItem?.height || 0);
    return {
      palletLatticeCode: listItem?.palletLatticeCode || '',
      palletLatticeHostCode: listItem?.palletLatticeHostCode || '',
      layer: index + 1 || '',
      height,
    }
  });
}

// 更新透传
function updateFormData() {
  console.log('updateFormData', curPalletLatticeList.value)
  props.updateValue(curPalletLatticeList.value);
}

function changeFormBase(index: number, item: {[k: string]: any }) {
  curPalletLatticeList.value.splice(index, 1, item)
  updateFormData()
}
</script>

<style scoped lang="scss">
.functionstyle {
  width: 100%;
}

.traySetItem {
  padding: 10px 0;
  border-bottom: 1px solid #ccc;
}
</style>