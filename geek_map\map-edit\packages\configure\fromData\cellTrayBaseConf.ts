import { usePureNumber, useCodeQualified } from "@packages/hook/useRules";
import { NodeAttrEditConf } from "@packages/type/editUiType";
import DICT from "@packages/configure/dict";
import { useI18n } from "@packages/hook/useI18n";
import { useAttrStore } from "@packages/store/attr"

export const TRAY_BASE_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  const { t } = useI18n();
  const sttrStore = useAttrStore();
  sttrStore.getContainerModelByOlny();

  return {
    name: "trayBase",
    tabTitle: "lang.rms.fed.basis",
    labelWidth: "85px",
    labelPosition: "left",
    formItem: [
      // 二维码单元格
      {
        prop: "isQrNode",
        label: t("lang.rms.web.map.cell.type.QRCell"),
        component: "elCheckbox",
        style: {
          width: "140px",
        },
        border: true,
        onlyComponent: true,
      },
      // 节点编码
      {
        prop: "cellCode",
        label: "lang.rms.fed.cellCode",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        rules: [usePureNumber()],
      },
      // 二维码码值
      {
        prop: "qrCode",
        label: "lang.rms.fed.qrCodeValue",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        rules: [usePureNumber()],
        appendAttrsFn(value: string, data: any) {
          return { condition: data.isQrNode };
        },
      },
      // 外部编码
      {
        prop: "hostCode",
        label: "lang.rms.fed.hostCode",
        component: "elInput",
        maxlength: 16,
        showWordLimit: true,
        rules: [useCodeQualified()],
      },
      // 节点类型
      {
        prop: "cellType",
        label: "lang.rms.fed.cellType",
        component: "elSelect",
        showWordLimit: true,
        appendAttrsFn(value: string) {
          const data = [];
          if (DICT.CELL_TYPE_DICT.find((item: { value: string }) => item.value === value)) {
            data.push(...DICT.CELL_TYPE_DICT);
          } else {
            const selTypeDict = DICT.CELL_TYPE_ALL_DICT.find((item: { value: string }) => item.value === value);
            data.push(selTypeDict, ...DICT.CELL_TYPE_DICT);
          }
          return { data };
        },
      },
      // 托盘架模型
      {
        prop: "palletRackModelId",
        label: "lang.rms.fed.palletFrameModel",
        component: "elSelect",
        data: sttrStore.containerModelDict || [],
        set(value: any[], allData: { [k: string]: any }) {
          allData.palletRackDto || (allData.palletRackDto = { angle: 0 });
          allData.palletRackDto.palletRackModelId = value;
          const containerModelDict = attrStore.containerModelDict || [];
          const palletLattices = containerModelDict.find((item: any) => {
            return item.value === value
          })
          allData.palletRackDto.palletLattices = palletLattices?.detail || [];
        },
        get(allData: { [k: string]: any }) {
          return allData.palletRackDto?.palletRackModelId
        }
      },
      // 托盘架编码
      {
        prop: "palletRackCode",
        label: "lang.rms.fed.palletRackCode",
        component: "elInput",
        maxlength: 16,
        showWordLimit: true,
        rules: [useCodeQualified()],
        set(value: any[], allData: { [k: string]: any }) {
          allData.palletRackDto || (allData.palletRackDto = { angle: 0 });
          allData.palletRackDto.palletRackCode = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.palletRackDto?.palletRackCode
        }
      },
      {
        prop: "angle",
        label: "lang.rms.palletRackManage.angle",
        component: "elInputNumber",
        min: 0,
        max: 365,
        set(value: any[], allData: { [k: string]: any }) {
          allData.palletRackDto || (allData.palletRackDto = { angle: 0 });
          allData.palletRackDto.angle = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.palletRackDto?.angle
        }
      },
      // 叉车角度
      {
        prop: "needAngles",
        label: "lang.rms.fed.needAngles",
        component: "elSelect",
        data: DICT.ANGLE_DICT,
        multiple: true,
      },
    ],
  };
};
