import { axios, Result } from "../axios";
import type { MapNodeDto } from "@packages/type/editNode";
import type { RequestOptions } from "@packages/api/axios/types";

/**************************
 * athena/map/draw/getMap *
 * ************************/
import type { GetMapParams, GetMapResult } from "./type/getMap";

export const getMap = (params: GetMapParams, option?: RequestOptions) => {
  return axios.post<Result<GetMapResult>>(
    {
      url: `/athena/map/draw/getMap?mapId=${params.mapId}&floorId=${params.floorId}`,
    },
    option,
  );
};

/****************************************
 * athena/map/version/getMapElementData *
 * **************************************/
import type {
  GetMapElementDataParams,
  GetMapElementDataResultItem,
} from "./type/getMapElementData";

export const getMapElementData = (params: GetMapElementDataParams) => {
  return axios.post<Result<GetMapElementDataResultItem>>({
    url: `/athena/map/version/getMapElementData`,
    params,
  });
};

/*********************
 * athena/dict/query *
 * *******************/
import type { QueryDictParams, QueryDictResult } from "./type/queryDict";
export const queryDict = (params: QueryDictParams) => {
  return axios.post<Result<QueryDictResult>>({
    url: "/athena/dict/query",
    params,
  });
};

/************************************
 * athena/map/draw/getAllRobotTypes *
 * **********************************/
export const getAllRobotTypes = () => {
  return axios.post<Result<string[]>>({
    url: "/athena/map/version/findRobotType",
  });
};

// STOP_MODE类型
export const getStopModeTypes = () => {
  const params = { dictCode: "STOP_MODE" };
  return axios.post({
    url: "/athena/dict/queryList",
    params,
  });
};

// 工作站类型
export const getStationTypeList = () => {
  return axios.get({ url: "/athena/station/stationType" });
};

//单行道类型
export const getSingleLaneTypes = () => {
  const params = { dictCode: "SINGLELANE_TYPE" };
  return axios.post({
    url: "/athena/dict/queryList",
    params,
  });
};

/*************************************
 * athena/map/draw/functional/labels *
 * ***********************************/
import { DrawFunctionalLabelsResult } from "./type/drawFunctionalLabels";

export const drawFunctionalLabels = () => {
  return axios.get<Result<DrawFunctionalLabelsResult>>({
    url: "/athena/map/draw/functional/labels",
  });
};

/**************************
 * athena/shelfModel/list *
 * ************************/
import { GetShelfModelListParams } from "./type/getShelfModelList";
export const getShelfModelList = (params: GetShelfModelListParams) => {
  return axios.get<Result<any[]>>({
    url: "/athena/shelfModel/list",
    params,
  });
};

/*************************************
 * athena/map/version/getFloorDetail *
 * ***********************************/
import { GetFloorDetailParams, GetFloorDetailResult } from "./type/getFloorDetail";

export const getFloorDetail = (params: GetFloorDetailParams, option?: RequestOptions) => {
  return axios.post<Result<GetFloorDetailResult>>(
    {
      url: `/athena/map/version/getFloorDetail?mapId=${params.mapId}&floorId=${params.floorId}`,
    },
    option,
  );
};

/**********************************
 * athena/map/draw/getAllMapAreas *
 * ********************************/
import { GetAllMapAreasParams } from "./type/getAllMapAreas";
export const getAllMapAreas = (params: GetAllMapAreasParams) => {
  return axios.post<Result<any[]>>({
    url: "/athena/map/draw/getAllMapAreas",
    params,
  });
};

/************************************************
 * 验证保存地图数据 athena/map/version/validateMap *
 * **********************************************/
import { ValidateMapParams, ValidateMapResult } from "./type/validateMap";
export const validateMap = (params: ValidateMapParams) => {
  return axios.post<Result<ValidateMapResult>>({
    url: "/athena/map/version/validateMap",
    params,
  });
};

/*****************************************
 * 保存地图数据 athena/map/version/saveMap *
 * ***************************************/
export const saveMap = (params: ValidateMapParams) => {
  return axios.post<Result<null>>({
    url: "/athena/map/version/saveMap",
    params,
  });
};

/*********************************************
 * 查询机器人数据 athena/map/version/queryRobot *
 * *******************************************/
import { QueryRobotParams, QueryRobotResult } from "./type/queryRobot";
export const queryRobot = (params: QueryRobotParams) => {
  return axios.get<Result<null | QueryRobotResult>>({
    url: "/athena/map/version/queryRobot",
    params,
  });
};

/****************************************
 * 查询货架模型数据 athena/shelfModel/list *
 * **************************************/
import { GetShelfModelParams } from "./type/getShelfModel";
export const getShelfModel = (params: GetShelfModelParams) => {
  return axios.get<Result<any[]>>({
    url: "/athena/shelfModel/list",
    params,
  });
};

/***************************************************
 * 查询货架模型数据 athena/map/draw/functional/labels *
 * *************************************************/
import { GetFunctionalLabelsResult } from "./type/getFunctionalLabels";
export const getFunctionalLabels = () => {
  return axios.get<Result<GetFunctionalLabelsResult[]>>({
    url: "/athena/map/draw/functional/labels",
  });
};

/*******************************************************
 *      校验区域内元素 athena/map/version/screenNoList    *
 * *****************************************************/
import { ScreenNoListParams } from "./type/screenNoList";
export const screenNoList = (params: ScreenNoListParams) => {
  return axios.post<Result<string[]>>({
    url: "/athena/map/version/screenNoList",
    params,
  });
};
/*******************************************************
 *      删除区域时调用 athena/map/draw/deleteArea   *
 * *****************************************************/
export const deleteArea = (params: any) => {
  return axios.post<Result<string[]>>({
    url: "/athena/map/draw/deleteArea",
    params,
  });
};
/****************************************************************
 *      获取可行驶类型 athena/robot/manage/findDistinctSizeType    *
 * **************************************************************/
export const findDistinctSizeType = () => {
  return axios.get<Result<string[]>>({
    url: "/athena/robot/manage/findDistinctSizeType",
  });
};

/****************************************************************
 *      避让可选点位 athena/map/version/findAdjacentByCellCode    *
 * **************************************************************/
import { FindAdjacentByCellCodeParams } from "./type/findAdjacentByCellCode";
export const findAdjacentByCellCode = (params: FindAdjacentByCellCodeParams) => {
  return axios.get<Result<MapNodeDto[]>>({
    url: "/athena/map/version/findAdjacentByCellCode",
    params,
  });
};

/****************************************************
 *      支持的指令类型 athena/dict/robotInstruction    *
 * **************************************************/
export const robotInstruction = () => {
  return axios.get<Result<string[]>>({
    url: "/athena/dict/robotInstruction",
  });
};

/***********************************************
 *       获取下一个ID athena/sequence/nextId     *
 * *********************************************/
import { SequenceNextIdParams } from "./type/sequenceNextId";
export const sequenceNextId = (params: SequenceNextIdParams) => {
  return axios.get<Result<number>>({
    url: "/athena/sequence/nextId",
    params,
  });
};

/*************************************************************
 *  单行道独木桥检查 athena/map/version/generatorNodeDirection  *
 * **********************************************************/
import { GeneratorNodeDirectionParams } from "./type/generatorNodeDirection";
export const generatorNodeDirection = (params: GeneratorNodeDirectionParams) => {
  return axios.post<Result<any[]>>({
    // url: "/athena/map/version/generatorNodeDirection",
    url: "/athena/map/version/getNodeInfo",
    params,
  });
};

/***************************************************
 *  反光柱检查 /athena/map/version/getReflectorInfo  *
 * *************************************************/
import { GetReflectorInfoParams, GetReflectorInfoResultItem } from "./type/getReflectorInfo";
export const getReflectorInfo = (params: GetReflectorInfoParams) => {
  return axios.post<Result<GetReflectorInfoResultItem[] | null>>({
    url: "/athena/map/version/getReflectorInfo",
    params,
  });
};

/*********************************************
 *       全局筛选 /athena/map/fastSearch       *
 * *******************************************/
import { FastSearchParams, FastSearchResultItem } from "./type/fastSearch";
export const fastSearch = (params: FastSearchParams) => {
  return axios.post<Result<FastSearchResultItem[]>>({
    url: "/athena/map/fastSearch",
    params,
  });
};

/************************************************************************
 *      支持的指令类型 athena/containerModel/findContainerModelByPage    *
 * **********************************************************************/
import {
  FindContainerModelByPageParams,
  FindContainerModelByPageResult,
} from "./type/findContainerModelByPage";
export const findContainerModelByPage = (params: FindContainerModelByPageParams) => {
  return axios.post<Result<FindContainerModelByPageResult>>({
    url: "/athena/containerModel/findContainerModelByPage",
    params,
  });
};

/************************************************************
 *   查询托盘架 athena/palletRack/detail/:mapId/:cellCode    *
 * **********************************************************/
import { PalletRackDetailResult, PalletRackDetailParams } from "./type/palletRackDetail";
export const palletRackDetail = (params: PalletRackDetailParams) => {
  return axios.get<Result<PalletRackDetailResult>>({
    url: `/athena/palletRack/detail/${params.mapId}/${params.cellCode}`,
  });
};
/****************************************
 * athena/station/findPppStation *
 * **************************************/

export const findPppStation = (params: any) => {
  return axios.get<Result<any>>({
    url: `/athena/station/findPppStation`,
    params,
  });
};

/****************************************
 * athena/station/updatePppStation *
 * **************************************/

export const updatePppStation = (params: any) => {
  return axios.post<Result<any>>({
    url: `/athena/station/updatePppStation`,
    params,
  });
};

/****************************************
 * athena/deviceEdit/queryChildDeviceInfo *
 * **************************************/

export const queryChildDeviceInfo = (params: any) => {
  return axios.get<Result<any>>({
    url: `/athena/deviceEdit/queryChildDeviceInfo`,
    params,
  });
};

/****************************************
 * athena/deviceEdit/saveAndUpdateDevice  *
 * **************************************/

export const updateXDevice = (params: any) => {
  return axios.post<Result<any>>({
    url: `/athena/deviceEdit/saveAndUpdateDevice`,
    params,
  });
};

/****************************************
 * athena/deviceInfo/vsw/findVswStation *
 * **************************************/

export const findVswStation = (params: any) => {
  return axios.get<Result<any>>({
    url: `/athena/deviceInfo/vsw/findVswStation`,
    params,
  });
};

/****************************************
 * athena/deviceInfo/vsw/updateVswStation *
 * **************************************/

export const updateVswStation = (params: any) => {
  return axios.post<Result<any>>({
    url: `/athena/deviceInfo/vsw/updateVswStation`,
    params,
  });
};
