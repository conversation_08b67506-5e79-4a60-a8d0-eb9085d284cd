<template>
  <div class="title">{{ $t(title) }}</div>
  <el-scrollbar>
    <div class="attrPanel pppstaionPanel">
      <el-form ref="formRef" class="attr-panel-form" :model="formData" :rules="rules">
        <el-form-item :label="$t('lang.rms.fed.stationNo')" class="font-weight-700">
          <span>{{ formData.stationId }}</span>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.map.deviceEdit.deviceVersion')">
          <!-- {{ formData?.deviceModel?.modelCode }} -->
          <div v-for="item in formData.deviceModelOptionList">
            <span :key="item.modelCode" v-if="item.modelCode === formData?.deviceModel?.modelCode">
              {{ $t(item.langItem) }}
            </span>
          </div>
        </el-form-item>

        <!-- x:xlocationOffset y:ylocationOffset -->
        <LocationXYForDiffTitle
          title="lang.rms.map.deviceEdit.deviceOffset"
          xKey="xlocationOffset"
          yKey="ylocationOffset"
          :curItem="formData"
          :viewDevice="viewDevice"
        />
        <el-form-item :label="$t('lang.rms.device.shelfControlledByRMS')" class="ppp-relatvie-box">
          <el-select v-model="formData.shelfControlledByRMS" :disabled="viewDevice">
            <el-option key="1" :label="$t('lang.rms.fed.yes')" :value="1" />
            <el-option key="0" :label="$t('lang.rms.fed.no')" :value="0" />
          </el-select>
          <el-popover
            placement="top-end"
            :width="200"
            trigger="click"
            :content="t('lang.rms.fed.ppp.stationModel.shelfControlledByRMSDesc')"
          >
            <template #reference>
              <span class="ppp-question"> </span>
            </template>
          </el-popover>
        </el-form-item>

        <!-- 理货避让 -->
        <template v-if="formData.transferConfig">
          <el-form-item
            :label="$t('lang.rms.map.deviceEdit.tallyAvoidance')"
            class="font-weight-700"
          >
            <el-checkbox :disabled="viewDevice" v-model="formData.transferConfig.copyAllStation">
              {{ $t("lang.rms.map.deviceEdit.copyForOthers") }}
            </el-checkbox>
          </el-form-item>
          <!-- 左上角 x y -->
          <LocationXYForDiffTitle
            title="lang.rms.map.deviceEdit.leftTopAvoidance"
            xKey="leftTopX"
            yKey="leftTopY"
            :curItem="formData.transferConfig"
            :viewDevice="viewDevice"
          />
          <!-- 左下角 x y -->
          <LocationXYForDiffTitle
            title="lang.rms.map.deviceEdit.leftBottomAvoidance"
            xKey="leftBottomX"
            yKey="leftBottomY"
            :curItem="formData.transferConfig"
            :viewDevice="viewDevice"
          />
          <!-- 右上角 x y -->
          <LocationXYForDiffTitle
            title="lang.rms.map.deviceEdit.rightTopAvoidance"
            xKey="rightTopX"
            yKey="rightTopY"
            :curItem="formData.transferConfig"
            :viewDevice="viewDevice"
          />
          <!-- 右下角 x y -->
          <LocationXYForDiffTitle
            title="lang.rms.map.deviceEdit.rightBottomAvoidance"
            xKey="rightBottomX"
            yKey="rightBottomY"
            :curItem="formData.transferConfig"
            :viewDevice="viewDevice"
          />
          <!-- 避让安全距离 -->
          <LocationXYForDiffTitle
            title="lang.rms.map.deviceEdit.avoidingSafeDistance"
            xKey="safeX"
            yKey="safeY"
            :curItem="formData.transferConfig"
            :viewDevice="viewDevice"
          />
        </template>
        <!-- ppp左、右侧停靠点 -->
        <div class="demo-collapse">
          <el-collapse v-model="activeNames" @change="handleChange">
            <!-- ppp左侧停靠点 -->
            <el-collapse-item :title="$t(pppParkTitle)" name="1">
              <el-form-item :label="$t('lang.rms.web.station.parkId')">
                <span>{{ formData.left.parkIds }}</span>
              </el-form-item>
              <el-form-item :label="$t('lang.venus.web.common.deviceCode')" prop="left.plcCode">
                <el-input
                  v-model="formData.left.plcCode"
                  :disabled="viewDevice"
                  maxlength="4"
                  :placeholder="$t('lang.rms.map.deviceEdit.autoGeneratedAndModifyMaxLenAny', [4])"
                  show-word-limit
                />
              </el-form-item>
              <cache-bit
                ref="leftCacheBitRef"
                :isPppLite="pppLite"
                :options="formData.left"
                :device-model="curData.deviceModel"
                :view-device="viewDevice"
              />
            </el-collapse-item>
            <!-- ppp右侧停靠点 -->
            <el-collapse-item
              v-if="formData.right"
              :title="$t('lang.rms.map.deviceEdit.pppRightPark')"
              name="2"
            >
              <el-form-item :label="$t('lang.rms.web.station.parkId')">
                <span>{{ formData.right?.parkIds }}</span>
              </el-form-item>
              <el-form-item :label="$t('lang.venus.web.common.deviceCode')" prop="right.plcCode">
                <el-input
                  v-model="formData.right.plcCode"
                  maxlength="4"
                  :disabled="viewDevice"
                  :placeholder="$t('lang.rms.map.deviceEdit.autoGeneratedAndModifyMaxLenAny', [4])"
                  show-word-limit
                />
              </el-form-item>
              <cache-bit
                ref="rightCacheBitRef"
                :isPppLite="pppLite"
                :options="formData.right"
                :device-model="curData.deviceModel"
                :view-device="viewDevice"
              />
            </el-collapse-item>
            <!-- ppp扩展停靠点 -->
            <el-collapse-item
              v-if="formData.extendParkIds"
              :title="$t('lang.rms.map.deviceEdit.extensionPark')"
              name="3"
            >
              <template v-for="park in formData.extendParkIds">
                <el-form-item :label="$t('lang.rms.web.station.parkId')">
                  <span>{{ park }}</span>
                </el-form-item>
              </template>
            </el-collapse-item>
          </el-collapse>
        </div>

        <el-form-item v-if="!viewDevice" class="attr-button-area">
          <el-button class="fRight" type="primary" @click="onSubmit(formRef)">{{
            $t("lang.rms.fed.confirm")
          }}</el-button>
          <el-button class="fRight" @click="resetForm(formRef)">{{
            $t("lang.rms.fed.cancel")
          }}</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, ComputedRef, ref, watch, reactive } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useI18n } from "@packages/hook/useI18n";
import { useAttrStore } from "@packages/store/attr";
import { updatePppStation } from "@packages/api/map";
import LocationXYForDiffTitle from "./components/locationXYForDiffTitle.vue";
import CacheBit from "./components/cacheBit.vue";

const { t } = useI18n();
const attrStore = useAttrStore();
const attrStoreRef = storeToRefs(useAttrStore());

const leftCacheBitRef = ref(null);
const rightCacheBitRef = ref(null);

let activeNames = ref(["1", "2"]);

const props = defineProps<{
  curData: any;
  title: string;
  viewDevice: boolean;
}>();

let formData = computed(() => {
  //Form 表单的数据
  const leftLatticeInfos = props.curData.left.latticeInfos;
  const rightLatticeInfos = props.curData.right?.latticeInfos;
  let leftLayout: any = getStationLayout(leftLatticeInfos);
  let rightLayout = getStationLayout(rightLatticeInfos);
  props.curData.leftLayout = leftLayout;
  props.curData.rightLayout = rightLayout;

  return props.curData;
});

//计算属性ppLite， 根据props.curData.right?.latticeInfos是否为null，返回true或false
const pppLite: ComputedRef<boolean> = computed(() => {
  return !props.curData.right?.latticeInfos;
});

// 判断pppLite是否为true，如果是，则显示“ppp停靠点”，否则显示ppp左侧停靠点
const pppParkTitle = computed(() => {
  return pppLite.value
    ? t("lang.rms.map.deviceEdit.pppPark")
    : t("lang.rms.map.deviceEdit.pppLeftPark");
});

const getStationLayout = (latticeInfos: any) => {
  let pLine = 0;
  let pColumn = 0;
  let result: any = {};
  (latticeInfos || []).map((item: any) => {
    console.log(pLine, item);
    pColumn = item.layerColumnNum;

    if (item.line != pLine) {
      pLine = item.line;
      result[pLine] = [];
      result[pLine].push({
        layout: pLine + "00" + pColumn,
        ...item,
      });
    } else {
      result[pLine].push({
        layout: pLine + "00" + pColumn,
        ...item,
      });
    }
  });

  const layoutArray: any = [];
  for (const i in result) {
    const element = result[i];
    layoutArray.push(element);
  }

  return layoutArray;
};

const curSelectNode = ref(attrStoreRef.curNodeDataByIndex.value);
console.log("curSelectNode.value", curSelectNode.value);

const handleChange = (val: string[]) => {
  console.log(val);
};

const diffTitleLocationChange = () => {
  console.log();
};

const formValidateFn = () => {
  console.log(formData.value.left.plcCode);
  if (formData.value.left.plcCode.length > 4 || formData.value.right.plcCode.length > 4) {
    ElMessage({
      message: t("lang.rms.fed.limitLength", [t("lang.venus.web.common.deviceCode"), 4]),
      type: "error",
    });
    return false;
  }
  return true;
};

const rules = reactive<FormRules>({
  "left.plcCode": [
    { required: true, message: t("lang.rms.fed.pleaseEnter"), trigger: "change" },
    {
      max: 4,
      message: t("lang.rms.map.deviceEdit.autoGeneratedAndModifyMaxLenAny", [4]),
      trigger: "change",
    },
  ],
  "right.plcCode": [
    { required: true, message: t("lang.rms.fed.pleaseEnter"), trigger: "change" },
    {
      max: 4,
      message: t("lang.rms.map.deviceEdit.autoGeneratedAndModifyMaxLenAny", [4]),
      trigger: "change",
    },
  ],
});

const formRef = ref();
const onSubmit = async (formEl: FormInstance | undefined) => {
  console.log("submit!", formEl, formData.value);

  // 获取数据

  if (!formEl) return;
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      let leftCacheData: any = {};
      let rightCacheData: any = {};
      const reqData = { ...formData.value };
      if (leftCacheBitRef.value) {
        leftCacheData = (leftCacheBitRef.value as any).getData(reqData.left.parkId);
        reqData.left = { ...reqData.left, ...leftCacheData };

        const isLeftNotData = leftCacheData.transferLatticeInfos.find((item: any) => {
          return item.locationX === null || item.locationY === null;
        });

        if (isLeftNotData) {
          ElMessage({
            message: t("lang.rms.fed.cacheBitCoordEmpty"),
            type: "error",
          });
          return;
        }
      }

      if (rightCacheBitRef.value) {
        rightCacheData = (rightCacheBitRef.value as any).getData(reqData.right.parkId);
        reqData.right = { ...reqData.right, ...rightCacheData };
        const isLeftNotData = leftCacheData.transferLatticeInfos.find((item: any) => {
          return item.locationX === null || item.locationY === null;
        });

        if (isLeftNotData) {
          ElMessage({
            message: t("lang.rms.fed.cacheBitCoordEmpty"),
            type: "error",
          });
          return;
        }
      }

      console.log("submit!");
      console.log("fields", fields);
      updatePppStation(reqData).then(({ code, data, msg }) => {
        if (code === 0) {
          close();
          if (msg) {
            ElMessage({
              message: t(msg),
              type: "success",
            });
          }
        }
      });
    } else {
      console.log("error submit!", fields);
    }
  });

  // const is = await formValidateFn()
};

const switchOptionList = [
  { value: 1, label: "lang.rms.fed.yes" },
  { value: 0, label: "lang.rms.fed.no" },
];

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  close();
};

const close = () => {
  emits("trigger", null);
};
/**
 * 可用的事件
 */
const emits = defineEmits<{
  (event: "trigger", data: any): void;
}>();

defineExpose({
  // getRef,//外部调用的 一些方法
});
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;

  font-size: 0px;

  &.pppstaionPanel {
    padding-bottom: 30px;
  }
  .attr-panel-form {
    padding: 10px;

    .font-weight-700 {
      font-weight: 700;
    }

    .ppp-relatvie-box {
      position: relative;

      :deep(.el-form-item__content) {
        padding-right: 23px;
      }
      .ppp-question {
        position: absolute;
        right: 0;
        top: 5px;
        display: block;
        width: 20px;
        height: 20px;
        background: url(data:image/png;base64,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)
          no-repeat;
        background-size: 100% 100%;

        color: #efefef;
        cursor: pointer;
      }
    }
    .ppp-layout {
      display: flex;
      width: 100%;

      overflow: hidden;
      flex-direction: row;
      flex-grow: 1;
      margin-bottom: 15px;

      .ppp-layout-wrap-left {
        width: 10%;
        height: 100%;
      }
      .ppp-layout-wrap-right {
        width: 10%;
        height: 100%;
      }
      .ppp-layout-wrap-center {
        flex-grow: 1;
        display: flex;
        height: 100%;
        flex-direction: column;
      }

      .ppp-layout-title {
        width: 100%;
        overflow: hidden;
        font-weight: 700;
        text-align: center;
        font-size: 16px;
        height: 30px;
        line-height: 30px;
        background: #d9ecff;
      }

      .ppp-layout-con {
        flex-grow: 1;
        display: flex;
        flex-direction: row;
        width: 100%;
        height: auto;
        overflow: hidden;
        background: #ecf5ff;
        .ppp-layout-title {
          font-size: 14px;
        }

        .ppp-layout-inner-con {
          width: 100%;
          display: flex;
          // flex-wrap: wrap;
          flex-direction: column-reverse;
        }

        .ppp-layout-line {
          width: 100%;
          flex: 1;
          display: flex;
        }
        .ppp-layout-item {
          width: 49%;
          height: 30px;
          background: #c6e2ff;
          margin: 0.5%;
          font-size: 12px;
          line-height: 30px;
          text-align: center;
          &.long-lattice {
            width: 100%;
          }
        }
        .ppp-layout-con-left {
          flex: 1;
          height: 100%;
          overflow: hidden;
          margin: 1px;
        }
        .ppp-layout-con-right {
          flex: 1;
          height: 100%;
          overflow: hidden;
          margin: 1px;
        }
      }
    }
  }
  .title {
    font-size: 20px;
    padding: 10px 10px 15px 10px;
  }

  .attr-button-area {
    padding: 15px 0 0 0;
  }
  .fRight {
    float: right;
  }
}
</style>
<style lang="scss">
.my-imput--number {
  border: 1px solid #dcdfe6;
  width: 100%;
  display: flex;
  border-radius: 4px;
  .el-input-number-mini {
    flex: 1;
  }
  :deep(.el-input__inner) {
    border: none !important;
  }
  .define-append {
    width: 40px;
    display: inline-block;
    background: #f5f7fa;
    padding: 0px 3px;
    border-left: none;
    height: 32px;
    line-height: 32px;
    color: #909399;
    font-size: 12px;
    text-align: center;
  }
}
</style>
