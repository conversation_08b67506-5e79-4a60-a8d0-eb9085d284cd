<template>
  <!-- 全局点位编辑 -->
  <div class="globalCell">
    <div class="title">{{ $t("lang.rms.fed.globalCellEdit") }}</div>
    <FormBase ref="fromBaseRef" labelWidth="90px" :formItem="fromItem" v-model:formData="globalCellEditFrom" />
    <el-row class="btns">
      <el-col :span="12" class="btn">
        <el-button :loading="isAdjustmentLoading" type="primary" @click="save">{{ $t('lang.rms.fed.application') }}</el-button>
      </el-col>
      <el-col :span="12" class="btn">
        <el-button :loading="isAdjustmentLoading" @click="outGlobalCell">{{ $t('lang.rms.fed.exit') }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { cellEditConfFn } from "@packages/configure/globalCell.conf";
import FormBase from "@packages/components/map-form/formBase.vue";
import { useAttrStore } from "@packages/store/attr";
import { getMapValidateMap,saveEditMap } from "@packages/hook/useSave";
import { ref, computed,watch } from "vue";
import { useEditMap,reloadMapData } from "@packages/hook/useEdit";
const attrStore = useAttrStore();
const isAdjustmentLoading = ref(false);
const globalCellEditFrom = ref({
    intervalGap: 1,
    intervalAngle: 1,
    angle: 0,
    moveX: 0,
    moveY: 0,
    // baseX: 0,
    // baseY: 0,
    // opType: 1,
  },
);
//移动时的选项
const moveOps = computed(() => {
  const {moveX,moveY} = globalCellEditFrom.value
  return {
    moveX,
    moveY,
    opType: 1
  }
})
//旋转时的配置
const bkPos = ref({x:0,y:0})
const angleOps = computed(() => {
  const {angle,moveX,moveY} = globalCellEditFrom.value
  const {x,y} = bkPos.value
  return {
    baseX:x+moveX,
    baseY:y+moveY,
    angle,
    opType: 2,
  }
})
//全局点编辑，联动显示
watch(globalCellEditFrom,(value) => {
  const {angle,moveX,moveY} = value
  bkPos.value = editMap.value?.globalMove(moveX, moveY, angle);
})
const fromBaseRef = ref();
const editMap = useEditMap();
const fromItem = computed(() => {
  return cellEditConfFn();
});

function outGlobalCell() {
  attrStore.setGlobalCell(false);
  if (!attrStore.moveOperationDtos[0]) {
    editMap.value?.globalMove(0, 0, 0);
  }
}

function save() {
  // const params = fromBaseRef.value.getFormData();
  isAdjustmentLoading.value = true;
  //操作数据
  const opArr = [moveOps.value,angleOps.value];
  console.log(opArr);
  attrStore.setMoveOperationDtos(opArr);
  saveEditMap().then(() => {
    isAdjustmentLoading.value = false;
    outGlobalCell()
  }).catch(() => {
    isAdjustmentLoading.value = false;
    outGlobalCell()
  })
  // console.log(moveOps,angleOps)
  // return
  // getMapValidateMap(opArr).then(({ code, data }) => {
  //   isAdjustmentLoading.value = false;
  //   if (code === 0) {
  //     attrStore.setMoveOperationDtos(opArr);
  //     attrStore.setGlobalCell(false);
  //     editMap.value?.globalMove(params.baseX, params.baseY, params.angle);
  //   }
  // })
  // .catch(() => {
  //   isAdjustmentLoading.value = false;
  // });
}
</script>

<style scoped lang="scss">
.globalCell {
  width: 300px;
  padding: 10px;

  .title {
    font-size: 18px;
    font-weight: 900;
    margin-bottom: 20px;
  }

  .btns {
    margin-top: 20px;
    .btn {
      text-align: center;
    }
  }
}
</style>
