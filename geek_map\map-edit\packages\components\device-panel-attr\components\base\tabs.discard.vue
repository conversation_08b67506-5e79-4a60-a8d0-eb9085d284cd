<template>
  <div class="title">{{ $t(title || "") }}</div>
  <el-tabs stretch v-model="tabActive" class="attr-tabs" :type="tabType">
    <el-tab-pane v-for="tabItem in tabs" class="attr-pane-sty" :name="tabItem.name">
      <template #label>
        <span class="attr-tabs-pane-title">
          <span>{{ $t(tabItem.title) }}</span>
        </span>
      </template>

      <QueryForm
        ref="queryFormRefs"
        :form-data="innerFormData[tabItem.name]"
        @update:form-data="data => updateFromData(tabItem.name, data)"
        v-bind="tabItem"
      >
        <template #[slotName] v-for="slotName in appendSlots">
          <slot :name="slotName" />
        </template>
      </QueryForm>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
/**
 * ⭕ 已弃用
 */
import QueryForm from "./fromBase.vue";
import { useAttrs, useSlots, watch, Ref, ref } from "vue";

const slots = useSlots();
const appendSlots = Object.keys(slots);

/* 组件可以抛出的事件 */
const emits = defineEmits<{
  (event: "reset"): void;
  (event: "change", data: any): void;
  (event: string, data: any): void;
}>();

interface FormItemExPropsType {
  prop?: string; // ⭐prop 请不要重复⭐
  span?: number;
  label?: string;
  onlyComponent?: boolean; // 是否忽略label展示, 为了解决checked的显示效果
  condition?: boolean;
  describe?: string; // 描述
  component?: string;
  watch?: Function; // 动态attr
  divider?: boolean; // 是否是分割线
  appendSlotName?: string; // 追加的元素内容
  [key: string]: any;
}

interface FromDataType {
  [key: string]: any;
}

interface NodeAttrEditConf {
  name: string;
  title: string;
  formData?: FromDataType;
  formItem: FormItemExPropsType[];
  [key: string]: any;
}

interface NodeAttrEditItemConf {
  title?: string;
  tabType?: "card" | "border-card";
  tabs: NodeAttrEditConf[];
}

const props = defineProps<NodeAttrEditItemConf>();
const tabActive: Ref<string> = ref(props.tabs[0].name);
const innerFormData: Ref<FromDataType> = ref(parseValue(props.tabs));
const queryFormRefs = ref();

watch(
  () => props.tabs,
  value => {
    innerFormData.value = parseValue(value);
    reTabActive();
  },
  { immediate: true, deep: true },
);

const resetFormData = () => {
  emits("reset");

  innerFormData.value = parseValue(
    props.tabs.map(item => ({
      ...item,
      formData: {},
    })),
  );
};

/**
 * 解析出一个完整的formData group
 */
function parseValue(tabsValue: NodeAttrEditConf[]): FromDataType {
  const data: FromDataType = {};
  tabsValue.forEach(tabItem => {
    const { formData, formItem, name } = tabItem;
    const keys = formItem.map(item => item.prop).filter(key => !!key);
    const curFormData: FromDataType = formData as FromDataType;
    keys.forEach(key => {
      [key] || (curFormData[key as string] = "");
    });
    data[name] = formData;
    return data;
  });
  return data;
}

const clearValidate = () => {
  queryFormRefs.value.map((formElRef: any) => formElRef.clearValidate());
};

const getValidateFormModel = async () => {
  try {
    await Promise.all(
      queryFormRefs.value.map((formElRef: any) => formElRef.getValidateFormModel()),
    );
    return Promise.resolve(getFormData());
  } catch (error) {
    return Promise.reject();
  }
};

/**
 * 获取数据集合
 */
const getFormData = () => {
  let formData = {};
  queryFormRefs.value.forEach((fromElRef: any) => {
    formData = { ...formData, ...fromElRef.getFormData() };
  });
  return formData;
};

/**
 * 更新数据
 */
const updateFromData = (key: string, data: object) => {
  innerFormData.value = {
    ...innerFormData.value,
    [key]: { ...data },
  };

  queryFormRefs.value.forEach((element: any) => {
    element.name === key && element.update(data);
  });
  triggerChange(key, data);
};

function triggerChange(tabName: string, tabData: any) {
  let allData = {};
  Object.values(innerFormData.value).forEach(item => {
    allData = { ...allData, ...item };
  });
  emits("change", { tabName, tabData, allData });
}

function updated() {
  innerFormData.value = { ...innerFormData.value };
}

/**
 * 如果检查到当前选中的tab无效则自动更新选中的tab
 */
function reTabActive() {
  if (!props.tabs.find(item => item.name === tabActive.value)) {
    tabActive.value = props.tabs[0].name;
  }
}

defineExpose({
  resetFormData,
  clearValidate,
  getFormData,
  updateFromData,
  getValidateFormModel,
  updated,
  reTabActive,
});
</script>

<style scoped lang="scss">
.attr-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title {
  padding-left: 8px;
  font-weight: 900;
  font-size: 16px;
  padding-top: 5px;
  padding-bottom: 5px;
  border-left: 3px solid #409eff;
}
</style>

<style lang="scss">
.attr-tabs {
  &.el-tabs--top {
    .el-tabs__item {
      &.is-top:nth-child(2),
      &.is-top:last-child {
        padding-left: 20px;
        padding-right: 20px;
      }
    }
    .el-tabs__content {
      flex: 1;
      overflow-y: auto;
    }

    .attr-pane-sty {
      padding: 0px 10px;
      width: 300px;
      height: 100%;
      position: relative;
      overflow: auto;
    }
  }
  .el-form-item {
    &.is-error {
      .el-icon {
        &.el-input__icon {
          &.el-input__validateIcon {
            color: #f56c6c;
          }
        }
      }
    }

    .el-icon {
      &.el-input__icon {
        &.el-input__validateIcon {
          color: #67c23a;
        }
      }
    }
  }
}
</style>
